#!/usr/bin/env python3
"""
Performance Analysis Script for HAR file
Analyzes website performance issues from HAR data
"""

import json
import sys
from datetime import datetime

def analyze_har_performance(har_file_path):
    """Analyze performance metrics from HAR file"""
    
    with open(har_file_path, 'r') as f:
        har_data = json.load(f)
    
    log = har_data['log']
    entries = log['entries']
    pages = log['pages']
    
    print("=== CHEERS MARKETPLACE PERFORMANCE ANALYSIS ===\n")
    
    # Page-level metrics
    if pages:
        page = pages[0]
        timings = page['pageTimings']
        print("📊 PAGE LOAD METRICS:")
        print(f"   • DOM Content Loaded: {timings['onContentLoad']}ms")
        print(f"   • Page Load Complete: {timings['onLoad']}ms")
        print(f"   • Fully Loaded: {timings['_fullyLoaded']}ms")
        print()
    
    # Analyze individual resources
    print("🔍 RESOURCE ANALYSIS:")
    print("=" * 60)
    
    slow_resources = []
    large_resources = []
    total_size = 0
    total_time = 0
    
    for entry in entries:
        url = entry['request']['url']
        time = entry['time']
        size = entry['response']['content']['size']
        resource_type = entry['response']['content'].get('_resourceType', 'Unknown')
        
        total_size += size
        total_time += time
        
        # Identify slow resources (>50ms)
        if time > 50:
            slow_resources.append({
                'url': url,
                'time': time,
                'type': resource_type,
                'size': size
            })
        
        # Identify large resources (>10KB)
        if size > 10000:
            large_resources.append({
                'url': url,
                'size': size,
                'type': resource_type,
                'time': time
            })
    
    # Sort by performance impact
    slow_resources.sort(key=lambda x: x['time'], reverse=True)
    large_resources.sort(key=lambda x: x['size'], reverse=True)
    
    print(f"📈 SUMMARY METRICS:")
    print(f"   • Total Resources: {len(entries)}")
    print(f"   • Total Size: {total_size / 1024:.1f} KB")
    print(f"   • Total Load Time: {total_time:.1f}ms")
    print()
    
    # Slow resources analysis
    print("🐌 SLOW LOADING RESOURCES (>50ms):")
    if slow_resources:
        for i, resource in enumerate(slow_resources[:10], 1):
            url_short = resource['url'].split('/')[-1][:50]
            print(f"   {i:2d}. {resource['time']:6.1f}ms - {resource['type']:10s} - {url_short}")
    else:
        print("   ✅ No significantly slow resources found")
    print()
    
    # Large resources analysis
    print("📦 LARGE RESOURCES (>10KB):")
    if large_resources:
        for i, resource in enumerate(large_resources[:10], 1):
            url_short = resource['url'].split('/')[-1][:50]
            size_kb = resource['size'] / 1024
            print(f"   {i:2d}. {size_kb:6.1f}KB - {resource['type']:10s} - {url_short}")
    else:
        print("   ✅ No large resources found")
    print()
    
    # Performance recommendations
    print("💡 PERFORMANCE RECOMMENDATIONS:")
    print("=" * 60)
    
    recommendations = []
    
    # Check for slow CSS
    css_resources = [r for r in slow_resources if r['type'] == 'Stylesheet']
    if css_resources:
        recommendations.append("🎨 CSS Optimization:")
        recommendations.append("   • Consider inlining critical CSS")
        recommendations.append("   • Minify and compress CSS files")
        recommendations.append("   • Use CSS bundling to reduce requests")
    
    # Check for slow JavaScript
    js_resources = [r for r in slow_resources if r['type'] == 'Script']
    if js_resources:
        recommendations.append("⚡ JavaScript Optimization:")
        recommendations.append("   • Defer non-critical JavaScript")
        recommendations.append("   • Use async loading for third-party scripts")
        recommendations.append("   • Consider code splitting")
    
    # Check for large images
    image_resources = [r for r in large_resources if r['type'] == 'Image']
    if image_resources:
        recommendations.append("🖼️ Image Optimization:")
        recommendations.append("   • Implement responsive images with srcset")
        recommendations.append("   • Use modern formats (WebP, AVIF)")
        recommendations.append("   • Add lazy loading for below-fold images")
    
    # Check for multiple requests to same domain
    domains = {}
    for entry in entries:
        domain = entry['request']['url'].split('/')[2]
        domains[domain] = domains.get(domain, 0) + 1
    
    high_request_domains = {d: c for d, c in domains.items() if c > 5}
    if high_request_domains:
        recommendations.append("🔗 Request Optimization:")
        recommendations.append("   • Consider bundling resources from same domain")
        recommendations.append("   • Use HTTP/2 server push for critical resources")
    
    if not recommendations:
        recommendations.append("✅ Overall performance looks good!")
        recommendations.append("   • Continue monitoring for regressions")
    
    for rec in recommendations:
        print(rec)
    
    print("\n" + "=" * 60)
    print("Analysis complete! Focus on the slowest resources first.")

if __name__ == "__main__":
    analyze_har_performance("har(4).json")
