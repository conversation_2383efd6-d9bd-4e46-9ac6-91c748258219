import { defineConfig } from 'astro/config';
import cloudflare from '@astrojs/cloudflare';

export default defineConfig({
  output: 'server',
  adapter: cloudflare(),
  site: 'https://www.cheersmarketplace.com',

  // Performance optimizations
  build: {
    // Inline small assets for better performance
    inlineStylesheets: 'auto',

    // Split chunks for better caching
    rollupOptions: {
      output: {
        // Optimize chunk splitting
        manualChunks: {
          // Keep vendor code separate for better caching
          vendor: ['astro'],
        }
      }
    }
  },

  // Vite optimizations for development and build
  vite: {
    build: {
      // Minify JavaScript and CSS
      minify: 'esbuild',

      // Enable CSS code splitting
      cssCodeSplit: true,

      // Optimize chunk size
      chunkSizeWarningLimit: 1000,

      // Rollup options for better optimization
      rollupOptions: {
        output: {
          // Optimize asset naming for caching
          assetFileNames: (assetInfo) => {
            const info = assetInfo.name.split('.');
            const ext = info[info.length - 1];
            if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
              return `assets/images/[name]-[hash][extname]`;
            }
            if (/css/i.test(ext)) {
              return `assets/css/[name]-[hash][extname]`;
            }
            return `assets/[name]-[hash][extname]`;
          },
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js'
        }
      }
    },

    // Optimize dependencies
    optimizeDeps: {
      include: ['astro']
    }
  },

  // Experimental features for better performance
  experimental: {
    // Enable content collections optimization
    contentCollectionCache: true
  }
});
