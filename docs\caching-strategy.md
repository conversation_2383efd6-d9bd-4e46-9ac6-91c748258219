# Caching Strategy for Cheers Marketplace

This document outlines the comprehensive caching strategy implemented for optimal performance and user experience.

## Overview

Our caching strategy is designed to:
- Maximize performance through aggressive caching of static assets
- Ensure fresh content for dynamic pages
- Provide excellent Core Web Vitals scores
- Support SEO requirements
- Maintain security standards

## Caching Tiers

### 1. Immutable Assets (1 Year Cache)
**Cache-Control: `public, max-age=31536000, immutable`**

- **Framework Assets**: `/_astro/*` - Astro-generated files with hashes
- **Hashed CSS/JS**: `/assets/css/*`, `/assets/js/*` - Build-generated files
- **Web Fonts**: `*.woff2`, `*.woff` - Font files (1 month cache)

**Why**: These files have content-based hashes in their names. When content changes, the filename changes, so they can be cached indefinitely.

### 2. Static Assets (1 Week Cache)
**Cache-Control: `public, max-age=604800, stale-while-revalidate=86400`**

- **Images**: `/images/*`, `/assets/*`
- **Icons**: `/favicon.svg`
- **Other static content**

**Why**: Static assets change infrequently but may need updates. The `stale-while-revalidate` directive ensures users get fast responses while content is updated in the background.

### 3. Dynamic Content (Tiered Caching)

#### Homepage (1 Hour)
**Cache-Control: `public, max-age=3600, stale-while-revalidate=1800`**
- Frequently updated with new products and content
- Short cache with background revalidation

#### Product Pages (2 Hours)
**Cache-Control: `public, max-age=7200, stale-while-revalidate=3600`**
- Product information changes less frequently
- Longer cache for better performance

#### Static Pages (4 Hours)
**Cache-Control: `public, max-age=14400, stale-while-revalidate=7200`**
- About, FAQ, Terms, Privacy pages
- Content rarely changes

### 4. API Endpoints

#### Product Data API (5 Minutes)
**Cache-Control: `public, max-age=300, s-maxage=300, stale-while-revalidate=600`**
- `/api/products.json`
- Balances freshness with performance

#### Other APIs (1 Minute)
**Cache-Control: `public, max-age=60, s-maxage=60, stale-while-revalidate=120`**
- General API endpoints
- Short cache for dynamic data

### 5. SEO Files (1 Day)
**Cache-Control: `public, max-age=86400`**

- **Sitemap**: `/sitemap.xml`
- **Robots**: `/robots.txt`

### 6. No Cache Areas
**Cache-Control: `no-cache, no-store, must-revalidate, private`**

- **Admin Panel**: `/admin/*`
- **Debug Pages**: `/debug-sync`
- **Sensitive areas**

## Cache Tags

We use Cloudflare's cache tags for selective purging:

- `products` - All product-related content
- `api` - API endpoints
- `html` - HTML pages
- `pages` - General page content
- `static` - Static pages
- `seo` - SEO-related files

### Purging Strategy

```bash
# Purge all product content when products update
curl -X POST "https://api.cloudflare.com/client/v4/zones/{zone_id}/purge_cache" \
  -H "Authorization: Bearer {api_token}" \
  -H "Content-Type: application/json" \
  --data '{"tags":["products"]}'

# Purge specific page types
curl -X POST "https://api.cloudflare.com/client/v4/zones/{zone_id}/purge_cache" \
  -H "Authorization: Bearer {api_token}" \
  -H "Content-Type: application/json" \
  --data '{"tags":["homepage"]}'
```

## Security Headers

All pages include comprehensive security headers:

- **X-Frame-Options**: `DENY` - Prevents clickjacking
- **X-Content-Type-Options**: `nosniff` - Prevents MIME sniffing
- **Referrer-Policy**: `strict-origin-when-cross-origin` - Controls referrer information
- **Permissions-Policy**: Restricts browser features
- **Content-Security-Policy**: Controls resource loading (homepage only)

## Performance Benefits

### Expected Improvements:
- **First Load**: Fast due to optimized caching headers
- **Repeat Visits**: Extremely fast due to cached assets
- **Background Updates**: `stale-while-revalidate` ensures fresh content without blocking
- **CDN Efficiency**: Proper cache headers maximize Cloudflare's edge caching

### Core Web Vitals Impact:
- **LCP (Largest Contentful Paint)**: Improved by cached images and CSS
- **FID (First Input Delay)**: Better due to cached JavaScript
- **CLS (Cumulative Layout Shift)**: Stable due to consistent asset loading

## Monitoring

### Key Metrics to Track:
1. **Cache Hit Ratio**: Should be >90% for static assets
2. **Time to First Byte (TTFB)**: Should be <200ms for cached content
3. **Asset Load Times**: Monitor for regression
4. **Cache Purge Frequency**: Track purge operations

### Tools:
- Cloudflare Analytics
- Google PageSpeed Insights
- WebPageTest
- Lighthouse CI

## Best Practices

### For Developers:
1. **Use content hashing** for all build assets
2. **Implement cache busting** for updated content
3. **Test cache headers** in staging environment
4. **Monitor cache performance** regularly

### For Content Updates:
1. **Purge relevant cache tags** when updating products
2. **Use staged deployments** to test cache behavior
3. **Coordinate cache purges** with content updates

## Future Enhancements

1. **Service Worker**: Implement for offline support
2. **HTTP/3**: Leverage when widely supported
3. **Edge Computing**: Move dynamic logic to edge
4. **Advanced Purging**: Implement automated cache invalidation

## Troubleshooting

### Common Issues:
1. **Stale Content**: Check cache tags and purge strategy
2. **Slow Loading**: Verify cache hit ratios
3. **Security Warnings**: Review security headers
4. **SEO Issues**: Check robots.txt and sitemap caching

### Debug Commands:
```bash
# Check cache headers
curl -I https://www.cheersmarketplace.com/

# Test specific asset caching
curl -I https://www.cheersmarketplace.com/assets/css/main.css

# Verify API caching
curl -I https://www.cheersmarketplace.com/api/products.json
```
