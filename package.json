{"name": "cheersmarketplace2", "type": "module", "version": "2.0.0", "description": "High-performance e-commerce website for Cheers Marketplace", "scripts": {"dev": "astro dev", "build": "astro build", "build:production": "NODE_ENV=production astro build", "preview": "astro preview", "astro": "astro", "analyze": "astro build --analyze", "check": "astro check"}, "dependencies": {"astro": "^5.12.3"}, "devDependencies": {"@astrojs/cloudflare": "^12.6.0", "wrangler": "^4.25.1"}, "engines": {"node": ">=18.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}