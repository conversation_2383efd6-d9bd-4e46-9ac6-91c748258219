/**
 * Simple Cart Feedback System
 * Handles visual feedback and button state updates for Snipcart integration
 */

window.SimpleCartFeedback = {
  cartItems: new Set(),
  lastCartCount: 0,
  
  init() {
    console.log('Initializing Simple Cart Feedback...');
    
    // Start monitoring immediately
    this.startMonitoring();
    
    // Listen for button clicks
    this.setupButtonListeners();
  },
  
  startMonitoring() {
    // Monitor cart count changes
    setInterval(() => {
      this.checkCartChanges();
    }, 500);
    
    // Monitor for Snipcart elements
    this.observeSnipcartElements();
  },
  
  setupButtonListeners() {
    document.addEventListener('click', (event) => {
      const button = event.target.closest('.snipcart-add-item');
      if (button) {
        this.handleButtonClick(button);
      }
    });
  },
  
  handleButtonClick(button) {
    const productId = button.getAttribute('data-item-id');
    const productName = button.getAttribute('data-item-name');
    
    console.log('Add to cart clicked:', productId, productName);
    
    // Show immediate loading state
    this.updateButtonState(productId, 'loading');
    
    // Show success toast after a short delay (simulating add)
    setTimeout(() => {
      if (window.showCartSuccess) {
        window.showCartSuccess(productName);
      }
      this.updateButtonState(productId, 'added');
      this.cartItems.add(productId);
      this.animateCartIcon();
    }, 800);
  },
  
  checkCartChanges() {
    // Check cart count from Snipcart
    const cartCountElement = document.querySelector('.snipcart-items-count');
    if (cartCountElement) {
      const currentCount = parseInt(cartCountElement.textContent) || 0;
      
      if (currentCount !== this.lastCartCount) {
        console.log('Cart count changed:', this.lastCartCount, '->', currentCount);
        this.lastCartCount = currentCount;
        
        if (currentCount === 0) {
          // Cart was cleared
          this.handleCartCleared();
        }
      }
    }
    
    // Also check if Snipcart API is available
    if (window.Snipcart && window.Snipcart.api) {
      try {
        const items = window.Snipcart.api.items.all();
        const currentItemIds = new Set(items.map(item => item.id));
        
        // Check for removed items
        this.cartItems.forEach(itemId => {
          if (!currentItemIds.has(itemId)) {
            console.log('Item removed from cart:', itemId);
            this.cartItems.delete(itemId);
            this.updateButtonState(itemId, 'available');
          }
        });
        
        // Check for added items (that we might have missed)
        currentItemIds.forEach(itemId => {
          if (!this.cartItems.has(itemId)) {
            console.log('Item found in cart:', itemId);
            this.cartItems.add(itemId);
            this.updateButtonState(itemId, 'added');
          }
        });
      } catch (error) {
        // Snipcart API not ready yet
      }
    }
  },
  
  observeSnipcartElements() {
    // Watch for Snipcart elements being added to DOM
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === 1 && node.id === 'snipcart') {
            console.log('Snipcart element detected');
            // Snipcart is now in the DOM
          }
        });
      });
    });
    
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  },
  
  handleCartCleared() {
    console.log('Cart cleared');
    this.cartItems.clear();
    this.updateAllButtonStates();
  },
  
  updateButtonState(productId, state) {
    const buttons = document.querySelectorAll(`[data-item-id="${productId}"]`);
    console.log(`Updating ${buttons.length} buttons for product ${productId} to state: ${state}`);
    
    buttons.forEach(button => {
      button.classList.remove('btn-loading', 'btn-added', 'btn-available');
      
      const isLargeButton = button.classList.contains('btn-large');
      const iconSize = isLargeButton ? '20' : '16';
      
      switch (state) {
        case 'loading':
          button.classList.add('btn-loading');
          button.disabled = true;
          button.innerHTML = `
            <svg width="${iconSize}" height="${iconSize}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="animate-spin">
              <path d="M21 12a9 9 0 11-6.219-8.56"/>
            </svg>
            Adding...
          `;
          break;
          
        case 'added':
          button.classList.add('btn-added');
          button.disabled = true;
          button.innerHTML = `
            <svg width="${iconSize}" height="${iconSize}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M9 12l2 2 4-4"></path>
              <circle cx="12" cy="12" r="10"></circle>
            </svg>
            In Cart
          `;
          break;
          
        case 'available':
        default:
          button.classList.add('btn-available');
          button.disabled = false;
          const price = button.getAttribute('data-item-price');
          const priceText = price ? ` - $${parseFloat(price).toFixed(2)}` : '';
          button.innerHTML = `
            <svg width="${iconSize}" height="${iconSize}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="9" cy="21" r="1"></circle>
              <circle cx="20" cy="21" r="1"></circle>
              <path d="m1 1 4 4 2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
            </svg>
            Add to Cart${priceText}
          `;
          break;
      }
    });
  },
  
  updateAllButtonStates() {
    const allButtons = document.querySelectorAll('.snipcart-add-item');
    allButtons.forEach(button => {
      const productId = button.getAttribute('data-item-id');
      const state = this.cartItems.has(productId) ? 'added' : 'available';
      this.updateButtonState(productId, state);
    });
  },
  
  animateCartIcon() {
    const cartIcon = document.querySelector('.cart-trigger');
    if (cartIcon) {
      cartIcon.classList.add('cart-bounce');
      setTimeout(() => {
        cartIcon.classList.remove('cart-bounce');
      }, 600);
    }
  }
};

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.SimpleCartFeedback.init();
  });
} else {
  window.SimpleCartFeedback.init();
}
