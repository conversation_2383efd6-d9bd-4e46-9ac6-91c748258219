/**
 * Optimized Core JavaScript Bundle - Essential functionality for all pages
 * Version: 2.0 - Performance Optimized
 *
 * This bundle consolidates all critical functionality to minimize HTTP requests
 * and eliminate code duplication across the site.
 */

(function() {
  'use strict';

  // Prevent multiple initializations
  if (window.CheersMPCore) {
    console.warn('Core bundle already loaded, skipping duplicate initialization');
    return;
  }

  // Enhanced Cart Feedback System with better performance
  const CartFeedback = {
    cartItems: new Set(),
    lastCartCount: 0,
    isInitialized: false,

    init() {
      if (this.isInitialized) return;
      this.isInitialized = true;

      this.bindEvents();
      this.startMonitoring();
    },

    bindEvents() {
      // Listen for Snipcart events with better error handling
      document.addEventListener('snipcart.ready', () => {
        this.setupSnipcartEvents();
      });

      // Handle button clicks for immediate feedback
      document.addEventListener('click', (event) => {
        const button = event.target.closest('.snipcart-add-item');
        if (button) {
          this.handleButtonClick(button);
        }
      });
    },

    handleButtonClick(button) {
      const productId = button.getAttribute('data-item-id');
      const productName = button.getAttribute('data-item-name');

      // Show immediate loading state
      this.updateButtonState(productId, 'loading');

      // Show success feedback after brief delay
      setTimeout(() => {
        if (window.showToast) {
          window.showToast(
            'Added to Cart!',
            `${productName} added successfully`,
            'success',
            3000
          );
        }
        this.updateButtonState(productId, 'added');
        this.cartItems.add(productId);
        this.animateCartIcon();
      }, 600);
    },

    startMonitoring() {
      // Efficient cart monitoring with reduced frequency
      setInterval(() => {
        this.checkCartChanges();
      }, 1000);
    },

    checkCartChanges() {
      // Monitor cart count changes
      const cartCountElement = document.querySelector('.snipcart-items-count');
      if (cartCountElement) {
        const currentCount = parseInt(cartCountElement.textContent) || 0;

        if (currentCount !== this.lastCartCount) {
          this.lastCartCount = currentCount;

          if (currentCount === 0) {
            this.handleCartCleared();
          }
        }
      }

      // Sync with Snipcart API if available
      if (window.Snipcart?.api) {
        try {
          const items = window.Snipcart.api.items.all();
          const currentItemIds = new Set(items.map(item => item.id));

          // Update cart items tracking
          this.cartItems.forEach(itemId => {
            if (!currentItemIds.has(itemId)) {
              this.cartItems.delete(itemId);
              this.updateButtonState(itemId, 'available');
            }
          });

          currentItemIds.forEach(itemId => {
            if (!this.cartItems.has(itemId)) {
              this.cartItems.add(itemId);
              this.updateButtonState(itemId, 'added');
            }
          });
        } catch (error) {
          // Snipcart API not ready yet, continue monitoring
        }
      }
    },

    handleCartCleared() {
      this.cartItems.clear();
      this.updateAllButtonStates();
    },

    updateButtonState(productId, state) {
      const buttons = document.querySelectorAll(`[data-item-id="${productId}"]`);

      buttons.forEach(button => {
        button.classList.remove('btn-loading', 'btn-added', 'btn-available');

        const isLargeButton = button.classList.contains('btn-large');
        const iconSize = isLargeButton ? '20' : '16';

        switch (state) {
          case 'loading':
            button.classList.add('btn-loading');
            button.disabled = true;
            button.innerHTML = `
              <svg width="${iconSize}" height="${iconSize}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="animate-spin">
                <path d="M21 12a9 9 0 11-6.219-8.56"/>
              </svg>
              Adding...
            `;
            break;

          case 'added':
            button.classList.add('btn-added');
            button.disabled = true;
            button.innerHTML = `
              <svg width="${iconSize}" height="${iconSize}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M9 12l2 2 4-4"></path>
                <circle cx="12" cy="12" r="10"></circle>
              </svg>
              In Cart
            `;
            break;

          case 'available':
          default:
            button.classList.add('btn-available');
            button.disabled = false;
            const price = button.getAttribute('data-item-price');
            const priceText = price ? ` - $${parseFloat(price).toFixed(2)}` : '';
            button.innerHTML = `
              <svg width="${iconSize}" height="${iconSize}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="9" cy="21" r="1"></circle>
                <circle cx="20" cy="21" r="1"></circle>
                <path d="m1 1 4 4 2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
              </svg>
              Add to Cart${priceText}
            `;
            break;
        }
      });
    },

    updateAllButtonStates() {
      const allButtons = document.querySelectorAll('.snipcart-add-item');
      allButtons.forEach(button => {
        const productId = button.getAttribute('data-item-id');
        const state = this.cartItems.has(productId) ? 'added' : 'available';
        this.updateButtonState(productId, state);
      });
    },

    animateCartIcon() {
      const cartIcon = document.querySelector('.cart-trigger');
      if (cartIcon) {
        cartIcon.classList.add('cart-bounce');
        setTimeout(() => {
          cartIcon.classList.remove('cart-bounce');
        }, 600);
      }
    },

    setupSnipcartEvents() {
      if (typeof window.Snipcart === 'undefined') return;

      window.Snipcart.events.on('item.added', (cartItem) => {
        if (window.showToast) {
          window.showToast(
            'Added to Cart!',
            `${cartItem.name} added successfully`,
            'success',
            3000
          );
        }
      });

      window.Snipcart.events.on('item.removed', (cartItem) => {
        if (window.showToast) {
          window.showToast(
            'Removed from Cart',
            `${cartItem.name} has been removed`,
            'info',
            2000
          );
        }
      });

      window.Snipcart.events.on('cart.confirmed', () => {
        if (window.showToast) {
          window.showToast(
            'Order Confirmed!',
            'Thank you for your purchase. You will receive a confirmation email shortly.',
            'success',
            5000
          );
        }
      });
    }
  };

  // Enhanced Mobile Menu System with better performance and accessibility
  const MobileMenu = {
    isOpen: false,
    isInitialized: false,
    toggle: null,
    nav: null,

    init() {
      if (this.isInitialized) return;

      this.toggle = document.querySelector('.mobile-menu-toggle');
      this.nav = document.querySelector('.main-nav');

      if (!this.toggle || !this.nav) return;

      this.isInitialized = true;
      this.bindEvents();
    },

    bindEvents() {
      // Use bound methods for better performance
      this.handleToggleClick = this.handleToggleClick.bind(this);
      this.handleKeydown = this.handleKeydown.bind(this);
      this.handleOutsideClick = this.handleOutsideClick.bind(this);
      this.handleNavLinkClick = this.handleNavLinkClick.bind(this);

      this.toggle.addEventListener('click', this.handleToggleClick);
      document.addEventListener('keydown', this.handleKeydown);
      document.addEventListener('click', this.handleOutsideClick);
      this.nav.addEventListener('click', this.handleNavLinkClick);
    },

    handleToggleClick(e) {
      e.preventDefault();
      this.toggleMenu();
    },

    handleKeydown(e) {
      if (e.key === 'Escape' && this.isOpen) {
        this.closeMenu();
      }
    },

    handleOutsideClick(e) {
      if (this.isOpen && !this.nav.contains(e.target) && !this.toggle.contains(e.target)) {
        this.closeMenu();
      }
    },

    handleNavLinkClick(e) {
      if (e.target.tagName === 'A') {
        this.closeMenu();
      }
    },

    toggleMenu() {
      if (this.isOpen) {
        this.closeMenu();
      } else {
        this.openMenu();
      }
    },

    openMenu() {
      this.nav.classList.add('mobile-open', 'nav-open');
      this.toggle.classList.add('active', 'menu-open');
      this.toggle.setAttribute('aria-expanded', 'true');
      this.isOpen = true;

      // Prevent body scroll when menu is open
      document.body.style.overflow = 'hidden';

      // Animate hamburger lines with better performance
      const lines = this.toggle.querySelectorAll('.hamburger-line');
      if (lines.length >= 3) {
        lines[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
        lines[1].style.opacity = '0';
        lines[2].style.transform = 'rotate(-45deg) translate(7px, -6px)';
      }
    },

    closeMenu() {
      this.nav.classList.remove('mobile-open', 'nav-open');
      this.toggle.classList.remove('active', 'menu-open');
      this.toggle.setAttribute('aria-expanded', 'false');
      this.isOpen = false;

      // Restore body scroll
      document.body.style.overflow = '';

      // Reset hamburger lines
      const lines = this.toggle.querySelectorAll('.hamburger-line');
      if (lines.length >= 3) {
        lines[0].style.transform = '';
        lines[1].style.opacity = '';
        lines[2].style.transform = '';
      }
    },

    // Public API for external access
    open() { this.openMenu(); },
    close() { this.closeMenu(); },
    toggle() { this.toggleMenu(); }
  };

  // Enhanced Snipcart Initialization with better error handling
  const SnipcartInit = {
    isLoaded: false,
    isLoading: false,

    init() {
      this.setupInteractionListeners();
    },

    setupInteractionListeners() {
      // Load on first interaction with any cart element
      let hasInteracted = false;

      const loadOnInteraction = (e) => {
        if (!hasInteracted && e.target.closest('.snipcart-add-item, .snipcart-checkout, .cart-trigger')) {
          hasInteracted = true;
          this.loadSnipcart();
        }
      };

      document.addEventListener('mouseenter', loadOnInteraction, true);
      document.addEventListener('click', loadOnInteraction);
    },

    loadSnipcart() {
      if (this.isLoaded || this.isLoading) return;
      this.isLoading = true;

      const apiKey = window.SNIPCART_API_KEY;
      if (!apiKey) {
        console.warn('Snipcart API key not found');
        this.isLoading = false;
        return;
      }

      // Ensure SnipcartSettings exists
      if (!window.SnipcartSettings) {
        window.SnipcartSettings = {
          publicApiKey: apiKey,
          loadStrategy: "on-user-interaction",
          currency: "usd",
          addProductBehavior: "none",
          modalStyle: "side"
        };
      }

      // Create Snipcart container div if it doesn't exist
      if (!document.getElementById('snipcart')) {
        const div = document.createElement('div');
        div.id = 'snipcart';
        div.hidden = true;
        div.dataset.apiKey = apiKey;
        div.dataset.currency = 'usd';
        div.dataset.configAddProductBehavior = 'none';
        div.dataset.configModalStyle = 'side';
        document.body.appendChild(div);
      }

      // Load Snipcart resources with error handling
      Promise.all([
        this.loadCSS('https://cdn.snipcart.com/themes/v3.4.1/default/snipcart.css'),
        this.loadScript('https://cdn.snipcart.com/themes/v3.4.1/default/snipcart.js')
      ]).then(() => {
        this.isLoaded = true;
        this.isLoading = false;
        console.log('✓ Snipcart loaded successfully');
      }).catch((error) => {
        console.error('✗ Failed to load Snipcart:', error);
        this.isLoading = false;
      });
    },

    loadCSS(href) {
      return new Promise((resolve, reject) => {
        if (document.querySelector(`link[href="${href}"]`)) {
          resolve();
          return;
        }

        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = href;
        link.onload = resolve;
        link.onerror = reject;
        document.head.appendChild(link);
      });
    },

    loadScript(src) {
      return new Promise((resolve, reject) => {
        if (document.querySelector(`script[src="${src}"]`)) {
          resolve();
          return;
        }

        const script = document.createElement('script');
        script.src = src;
        script.async = true;
        script.defer = true;
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
      });
    }
  };

  // Lightweight Performance Monitoring
  const PerformanceMonitor = {
    init() {
      if (!('performance' in window)) return;

      // Monitor performance with minimal overhead
      window.addEventListener('load', () => {
        // Use requestIdleCallback for non-critical performance monitoring
        if ('requestIdleCallback' in window) {
          requestIdleCallback(() => this.measurePerformance());
        } else {
          setTimeout(() => this.measurePerformance(), 100);
        }
      });
    },

    measurePerformance() {
      try {
        const perfData = performance.getEntriesByType('navigation')[0];
        if (perfData) {
          // Store performance data for debugging (can be accessed via window.CheersMPCore.performance)
          this.lastMeasurement = {
            domContentLoaded: Math.round(perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart),
            loadComplete: Math.round(perfData.loadEventEnd - perfData.loadEventStart),
            totalLoadTime: Math.round(perfData.loadEventEnd - perfData.fetchStart),
            timestamp: Date.now()
          };
        }
      } catch (error) {
        console.warn('Performance measurement failed:', error);
      }
    }
  };

  // Optimized initialization with better error handling
  function initializeCore() {
    try {
      // Initialize cart feedback (always needed)
      CartFeedback.init();

      // Initialize Snipcart (lazy loaded)
      SnipcartInit.init();

      // Initialize performance monitoring (low priority)
      PerformanceMonitor.init();

      // Initialize mobile menu only when needed
      if (window.innerWidth <= 768 || document.querySelector('.mobile-menu-toggle')) {
        MobileMenu.init();
      }

      console.log('✓ Core bundle initialized successfully');
    } catch (error) {
      console.error('✗ Core bundle initialization failed:', error);
    }
  }

  // Smart initialization with better timing
  function smartInit() {
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initializeCore);
    } else {
      // DOM already loaded, initialize immediately
      initializeCore();
    }
  }

  // Expose enhanced global API
  window.CheersMPCore = {
    cart: CartFeedback,
    menu: MobileMenu,
    snipcart: SnipcartInit,
    performance: PerformanceMonitor,
    version: '2.0',
    initialized: false
  };

  // Maintain backward compatibility
  window.CheersMPUtils = window.CheersMPCore;

  // Initialize and mark as loaded
  smartInit();
  window.CheersMPCore.initialized = true;

})();
