// Core JavaScript Bundle - Critical functionality
// This bundle contains essential scripts that are needed on most pages

(function() {
  'use strict';

  // Note: Toast notifications are handled by the global window.showToast function
  // loaded from toast-notifications.js for consistency across the site

  // Cart Feedback System
  const CartFeedback = {
    init() {
      this.bindEvents();
    },
    
    bindEvents() {
      // Listen for Snipcart events
      document.addEventListener('snipcart.ready', () => {
        this.setupSnipcartEvents();
      });
    },
    
    setupSnipcartEvents() {
      if (typeof window.Snipcart === 'undefined') return;
      
      window.Snipcart.events.on('item.added', (cartItem) => {
        if (window.showToast) {
          window.showToast(
            'Added to Cart!',
            `${cartItem.name} added successfully`,
            'success',
            3000
          );
        }
      });

      window.Snipcart.events.on('item.removed', (cartItem) => {
        if (window.showToast) {
          window.showToast(
            'Removed from Cart',
            `${cartItem.name} has been removed`,
            'info',
            2000
          );
        }
      });

      window.Snipcart.events.on('cart.confirmed', () => {
        if (window.showToast) {
          window.showToast(
            'Order Confirmed!',
            'Thank you for your purchase. You will receive a confirmation email shortly.',
            'success',
            5000
          );
        }
      });
    }
  };

  // Mobile Menu System
  const MobileMenu = {
    isOpen: false,
    
    init() {
      this.bindEvents();
    },
    
    bindEvents() {
      const toggle = document.querySelector('.mobile-menu-toggle');
      const nav = document.querySelector('.main-nav');
      
      if (!toggle || !nav) return;
      
      toggle.addEventListener('click', () => {
        this.toggle(nav, toggle);
      });
      
      // Close on escape key
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && this.isOpen) {
          this.close(nav, toggle);
        }
      });
      
      // Close on outside click
      document.addEventListener('click', (e) => {
        if (this.isOpen && !nav.contains(e.target) && !toggle.contains(e.target)) {
          this.close(nav, toggle);
        }
      });
    },
    
    toggle(nav, toggle) {
      if (this.isOpen) {
        this.close(nav, toggle);
      } else {
        this.open(nav, toggle);
      }
    },
    
    open(nav, toggle) {
      nav.classList.add('mobile-open');
      toggle.classList.add('active');
      toggle.setAttribute('aria-expanded', 'true');
      this.isOpen = true;
      
      // Animate hamburger lines
      const lines = toggle.querySelectorAll('.hamburger-line');
      lines[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
      lines[1].style.opacity = '0';
      lines[2].style.transform = 'rotate(-45deg) translate(7px, -6px)';
    },
    
    close(nav, toggle) {
      nav.classList.remove('mobile-open');
      toggle.classList.remove('active');
      toggle.setAttribute('aria-expanded', 'false');
      this.isOpen = false;
      
      // Reset hamburger lines
      const lines = toggle.querySelectorAll('.hamburger-line');
      lines[0].style.transform = '';
      lines[1].style.opacity = '';
      lines[2].style.transform = '';
    }
  };

  // Snipcart Initialization
  const SnipcartInit = {
    init() {
      this.loadSnipcart();
    },
    
    loadSnipcart() {
      if (typeof window.Snipcart !== 'undefined') return;

      const apiKey = window.SNIPCART_API_KEY;
      if (!apiKey) {
        console.warn('Snipcart API key not found');
        return;
      }

      // Ensure SnipcartSettings exists (should be defined in Layout.astro)
      if (!window.SnipcartSettings) {
        window.SnipcartSettings = {
          publicApiKey: apiKey,
          loadStrategy: "on-user-interaction",
          currency: "usd",
          addProductBehavior: "none",
          modalStyle: "side"
        };
      }

      // Create Snipcart container div (required by Snipcart)
      if (!document.getElementById('snipcart')) {
        const div = document.createElement('div');
        div.id = 'snipcart';
        div.hidden = true;
        div.dataset.apiKey = apiKey;
        div.dataset.currency = 'usd';
        div.dataset.configAddProductBehavior = 'none';
        div.dataset.configModalStyle = 'side';
        document.body.appendChild(div);
      }

      // Load Snipcart CSS
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = 'https://cdn.snipcart.com/themes/v3.4.1/default/snipcart.css';
      document.head.appendChild(link);

      // Load Snipcart JS
      const script = document.createElement('script');
      script.src = 'https://cdn.snipcart.com/themes/v3.4.1/default/snipcart.js';
      script.async = true;
      script.defer = true;
      document.head.appendChild(script);
    }
  };

  // Performance Monitoring
  const PerformanceMonitor = {
    init() {
      this.measurePerformance();
    },
    
    measurePerformance() {
      if ('performance' in window) {
        window.addEventListener('load', () => {
          setTimeout(() => {
            const perfData = performance.getEntriesByType('navigation')[0];
            if (perfData) {
              // Performance data available for debugging if needed
              // Uncomment the line below for performance monitoring:
              // console.log('Page Performance:', { 'DOM Content Loaded': Math.round(perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart), 'Load Complete': Math.round(perfData.loadEventEnd - perfData.loadEventStart), 'Total Load Time': Math.round(perfData.loadEventEnd - perfData.fetchStart) });
            }
          }, 0);
        });
      }
    }
  };

  // Initialize all systems when DOM is ready
  function initializeCore() {
    CartFeedback.init();
    SnipcartInit.init();
    PerformanceMonitor.init();

    // Only initialize mobile menu on mobile devices
    if (window.innerWidth <= 768) {
      MobileMenu.init();
    }
  }

  // Initialize immediately if DOM is already loaded, otherwise wait
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeCore);
  } else {
    initializeCore();
  }

  // Expose utilities globally for other scripts
  window.CheersMPUtils = {
    cart: CartFeedback,
    menu: MobileMenu
  };

})();
