// Reliable Snipcart initialization with smart lazy loading
(function() {
  const key = window.SNIPCART_API_KEY || 'YTY0ZTIxNjYtNDVlMC00NmUyLWFkNjItYTg3ZmNhMTc4MDQyNjM4ODczNzI4NTM0MTY0NDA4';

  // Ensure SnipcartSettings exists (should be defined in Layout.astro)
  if (!window.SnipcartSettings) {
    window.SnipcartSettings = {
      publicApiKey: key,
      loadStrategy: "on-user-interaction",
      currency: "usd",
      addProductBehavior: "none",
      modalStyle: "side"
    };
  }

  let loaded = false;
  let loading = false;
  let cssLoaded = false;

  // Load CSS immediately for better UX
  function loadCSS() {
    if (cssLoaded) return;
    cssLoaded = true;

    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = 'https://cdn.snipcart.com/themes/v3.2.0/default/snipcart.css';
    link.media = 'print';
    link.onload = () => link.media = 'all';
    document.head.appendChild(link);
  }

  // Load Snipcart script and setup
  function loadSnipcart() {
    if (loaded || loading) return Promise.resolve();
    loading = true;

    return new Promise((resolve) => {
      loadCSS();

      // Create Snipcart container
      const div = document.createElement('div');
      div.id = 'snipcart';
      div.hidden = true;
      div.dataset.apiKey = key;
      div.dataset.currency = 'usd';
      div.dataset.configAddProductBehavior = 'none';
      div.dataset.configModalStyle = 'side';
      document.body.appendChild(div);

      // Load Snipcart script
      const script = document.createElement('script');
      script.src = 'https://cdn.snipcart.com/themes/v3.2.0/default/snipcart.js';
      script.async = true;
      script.onload = function() {
        loaded = true;
        loading = false;

        // Wait for Snipcart to be fully ready
        const checkReady = () => {
          if (window.Snipcart && window.Snipcart.api) {
            // Snipcart is ready - SimpleCartFeedback will handle cart management
            console.log('Snipcart is ready and available');
            resolve();
          } else {
            setTimeout(checkReady, 100);
          }
        };
        checkReady();
      };
      script.onerror = () => {
        loading = false;
        resolve();
      };
      document.head.appendChild(script);
    });
  }

  // Load on first interaction with any cart element
  let hasInteracted = false;
  document.addEventListener('mouseenter', e => {
    if (!hasInteracted && e.target.closest('.snipcart-add-item, .snipcart-checkout, .cart-trigger')) {
      hasInteracted = true;
      loadSnipcart();
    }
  }, true);

  // Handle clicks - load Snipcart if not loaded, but let clicks proceed normally
  document.addEventListener('click', e => {
    const cartElement = e.target.closest('.snipcart-add-item, .snipcart-checkout, .cart-trigger');
    if (cartElement) {
      if (!loaded && !loading) {
        // Start loading but don't prevent the click
        loadSnipcart();
      }
      // Let the click proceed normally - Snipcart will handle it when ready
    }
  });

  // Expose global function for manual loading
  window.loadSnipcart = loadSnipcart;
})();
