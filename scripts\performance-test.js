/**
 * Performance Testing Script for Cheers Marketplace
 * Tests the optimized website for performance improvements
 */

import { execSync } from 'child_process';
import { readFileSync, writeFileSync, existsSync } from 'fs';
import { join } from 'path';

class PerformanceTester {
  constructor() {
    this.results = {
      timestamp: new Date().toISOString(),
      version: '2.0',
      tests: {}
    };
  }

  log(message) {
    console.log(`🔍 ${message}`);
  }

  success(message) {
    console.log(`✅ ${message}`);
  }

  warning(message) {
    console.log(`⚠️  ${message}`);
  }

  error(message) {
    console.log(`❌ ${message}`);
  }

  // Test JavaScript bundle sizes
  testBundleSizes() {
    this.log('Testing JavaScript bundle sizes...');
    
    const bundleTests = {
      'core-bundle.js': { path: 'public/scripts/core-bundle.js', maxSize: 50000 },
      'products-bundle.js': { path: 'public/scripts/products-bundle.js', maxSize: 40000 },
      'toast-notifications.js': { path: 'public/scripts/toast-notifications.js', maxSize: 10000 }
    };

    const results = {};
    
    for (const [name, config] of Object.entries(bundleTests)) {
      if (existsSync(config.path)) {
        const content = readFileSync(config.path, 'utf8');
        const size = Buffer.byteLength(content, 'utf8');
        const gzipSize = this.estimateGzipSize(content);
        
        results[name] = {
          size,
          gzipSize,
          maxSize: config.maxSize,
          passed: size <= config.maxSize
        };

        if (size <= config.maxSize) {
          this.success(`${name}: ${size} bytes (${gzipSize} gzipped) ✓`);
        } else {
          this.warning(`${name}: ${size} bytes exceeds limit of ${config.maxSize}`);
        }
      } else {
        this.error(`${name}: File not found at ${config.path}`);
        results[name] = { error: 'File not found' };
      }
    }

    this.results.tests.bundleSizes = results;
  }

  // Estimate gzip compression size
  estimateGzipSize(content) {
    // Rough estimation: gzip typically achieves 60-80% compression for JS
    return Math.round(Buffer.byteLength(content, 'utf8') * 0.3);
  }

  // Test for duplicate code
  testCodeDuplication() {
    this.log('Testing for code duplication...');
    
    const duplicateTests = [
      { file: 'public/scripts/mobile-menu.js', shouldNotExist: true },
      { file: 'public/scripts/cart-feedback.js', shouldNotExist: true },
      { file: 'public/scripts/snipcart-init.js', shouldNotExist: true },
      { file: 'src/scripts/mobile-menu.js', shouldNotExist: true },
      { file: 'src/scripts/cart-feedback.js', shouldNotExist: true }
    ];

    const results = {};
    let duplicatesFound = 0;

    for (const test of duplicateTests) {
      const exists = existsSync(test.file);
      results[test.file] = {
        exists,
        shouldNotExist: test.shouldNotExist,
        passed: test.shouldNotExist ? !exists : exists
      };

      if (test.shouldNotExist && exists) {
        this.warning(`Duplicate file found: ${test.file}`);
        duplicatesFound++;
      } else if (test.shouldNotExist && !exists) {
        this.success(`Duplicate removed: ${test.file} ✓`);
      }
    }

    if (duplicatesFound === 0) {
      this.success('No duplicate files found ✓');
    }

    this.results.tests.codeDuplication = {
      duplicatesFound,
      details: results,
      passed: duplicatesFound === 0
    };
  }

  // Test core bundle functionality
  testCoreBundleFunctionality() {
    this.log('Testing core bundle functionality...');
    
    const coreBundlePath = 'public/scripts/core-bundle.js';
    if (!existsSync(coreBundlePath)) {
      this.error('Core bundle not found');
      this.results.tests.coreBundleFunctionality = { error: 'Core bundle not found' };
      return;
    }

    const content = readFileSync(coreBundlePath, 'utf8');
    
    const functionalityTests = [
      { name: 'CartFeedback', pattern: /const CartFeedback\s*=/, required: true },
      { name: 'MobileMenu', pattern: /const MobileMenu\s*=/, required: true },
      { name: 'SnipcartInit', pattern: /const SnipcartInit\s*=/, required: true },
      { name: 'PerformanceMonitor', pattern: /const PerformanceMonitor\s*=/, required: true },
      { name: 'Global API', pattern: /window\.CheersMPCore\s*=/, required: true },
      { name: 'Initialization', pattern: /initializeCore\(\)/, required: true },
      { name: 'Error Handling', pattern: /try\s*{[\s\S]*catch/, required: true },
      { name: 'Performance Optimization', pattern: /isInitialized/, required: true }
    ];

    const results = {};
    let passedTests = 0;

    for (const test of functionalityTests) {
      const found = test.pattern.test(content);
      results[test.name] = {
        found,
        required: test.required,
        passed: test.required ? found : true
      };

      if (found) {
        this.success(`${test.name}: Found ✓`);
        passedTests++;
      } else if (test.required) {
        this.error(`${test.name}: Missing`);
      }
    }

    this.results.tests.coreBundleFunctionality = {
      passedTests,
      totalTests: functionalityTests.length,
      details: results,
      passed: passedTests === functionalityTests.length
    };
  }

  // Test build configuration
  testBuildConfiguration() {
    this.log('Testing build configuration...');
    
    const configPath = 'astro.config.mjs';
    if (!existsSync(configPath)) {
      this.error('Astro config not found');
      this.results.tests.buildConfiguration = { error: 'Config not found' };
      return;
    }

    const content = readFileSync(configPath, 'utf8');
    
    const configTests = [
      { name: 'Minification', pattern: /minify:\s*['"]esbuild['"]/, required: true },
      { name: 'CSS Code Splitting', pattern: /cssCodeSplit:\s*true/, required: true },
      { name: 'Asset Optimization', pattern: /assetFileNames/, required: true },
      { name: 'Chunk Optimization', pattern: /chunkFileNames/, required: true },
      { name: 'Inline Stylesheets', pattern: /inlineStylesheets/, required: true }
    ];

    const results = {};
    let passedTests = 0;

    for (const test of configTests) {
      const found = test.pattern.test(content);
      results[test.name] = {
        found,
        required: test.required,
        passed: test.required ? found : true
      };

      if (found) {
        this.success(`${test.name}: Configured ✓`);
        passedTests++;
      } else if (test.required) {
        this.warning(`${test.name}: Not configured`);
      }
    }

    this.results.tests.buildConfiguration = {
      passedTests,
      totalTests: configTests.length,
      details: results,
      passed: passedTests >= configTests.length * 0.8 // 80% pass rate
    };
  }

  // Test component integration
  testComponentIntegration() {
    this.log('Testing component integration...');
    
    const componentTests = [
      { 
        file: 'src/components/Header.astro',
        patterns: [
          { name: 'No redundant mobile-menu.js', pattern: /mobile-menu\.js/, shouldNotExist: true },
          { name: 'Core bundle integration', pattern: /CheersMPCore/, required: true }
        ]
      },
      {
        file: 'src/components/SimpleCartFeedback.astro',
        patterns: [
          { name: 'No redundant cart-feedback.js', pattern: /cart-feedback\.js/, shouldNotExist: true },
          { name: 'Core bundle integration', pattern: /CheersMPCore/, required: true }
        ]
      }
    ];

    const results = {};
    let totalPassed = 0;
    let totalTests = 0;

    for (const component of componentTests) {
      if (!existsSync(component.file)) {
        this.warning(`Component not found: ${component.file}`);
        continue;
      }

      const content = readFileSync(component.file, 'utf8');
      const componentResults = {};

      for (const pattern of component.patterns) {
        totalTests++;
        const found = pattern.pattern.test(content);
        const passed = pattern.shouldNotExist ? !found : (pattern.required ? found : true);
        
        componentResults[pattern.name] = { found, passed };
        
        if (passed) {
          this.success(`${component.file} - ${pattern.name}: ✓`);
          totalPassed++;
        } else {
          this.warning(`${component.file} - ${pattern.name}: Failed`);
        }
      }

      results[component.file] = componentResults;
    }

    this.results.tests.componentIntegration = {
      passedTests: totalPassed,
      totalTests,
      details: results,
      passed: totalPassed === totalTests
    };
  }

  // Generate performance report
  generateReport() {
    this.log('Generating performance report...');
    
    const report = {
      ...this.results,
      summary: {
        totalTests: Object.keys(this.results.tests).length,
        passedTests: Object.values(this.results.tests).filter(test => test.passed).length,
        overallScore: 0
      }
    };

    report.summary.overallScore = Math.round(
      (report.summary.passedTests / report.summary.totalTests) * 100
    );

    // Write report to file
    const reportPath = 'docs/PERFORMANCE_TEST_RESULTS.md';
    const markdownReport = this.generateMarkdownReport(report);
    writeFileSync(reportPath, markdownReport);

    // Console summary
    console.log('\n📊 PERFORMANCE TEST RESULTS');
    console.log('================================');
    console.log(`Overall Score: ${report.summary.overallScore}%`);
    console.log(`Tests Passed: ${report.summary.passedTests}/${report.summary.totalTests}`);
    
    if (report.summary.overallScore >= 90) {
      this.success('Excellent performance optimization! 🚀');
    } else if (report.summary.overallScore >= 80) {
      this.success('Good performance optimization! 👍');
    } else {
      this.warning('Performance optimization needs improvement 🔧');
    }

    console.log(`\nDetailed report saved to: ${reportPath}`);
    
    return report;
  }

  generateMarkdownReport(report) {
    let markdown = `# Performance Test Results\n\n`;
    markdown += `**Generated:** ${report.timestamp}\n`;
    markdown += `**Version:** ${report.version}\n`;
    markdown += `**Overall Score:** ${report.summary.overallScore}%\n\n`;

    for (const [testName, testResults] of Object.entries(report.tests)) {
      markdown += `## ${testName.replace(/([A-Z])/g, ' $1').trim()}\n\n`;
      
      if (testResults.error) {
        markdown += `❌ **Error:** ${testResults.error}\n\n`;
        continue;
      }

      markdown += `**Status:** ${testResults.passed ? '✅ PASSED' : '❌ FAILED'}\n\n`;
      
      if (testResults.details) {
        markdown += `### Details\n\n`;
        for (const [key, value] of Object.entries(testResults.details)) {
          markdown += `- **${key}:** ${JSON.stringify(value, null, 2)}\n`;
        }
        markdown += '\n';
      }
    }

    return markdown;
  }

  // Run all tests
  async runAllTests() {
    console.log('🚀 Starting Performance Tests for Cheers Marketplace v2.0\n');
    
    this.testBundleSizes();
    this.testCodeDuplication();
    this.testCoreBundleFunctionality();
    this.testBuildConfiguration();
    this.testComponentIntegration();
    
    return this.generateReport();
  }
}

// Run tests if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const tester = new PerformanceTester();
  tester.runAllTests().catch(console.error);
}

export default PerformanceTester;
