/* Admin Panel Styles */

/* Modern Admin Panel Specific Styles */
.admin-tab-btn {
  background: var(--light-background);
  color: var(--text);
  border: 1px solid var(--border);
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.admin-tab-btn:hover {
  background: var(--border-light);
  border-color: var(--primary);
}

.admin-tab-btn.active {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
}

.btn-sync {
  background: #16a34a;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.btn-sync:hover {
  background: #15803d;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-sync:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-primary {
  background: var(--primary);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.btn-primary:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--light-background);
  color: var(--text);
  border: 1px solid var(--border);
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.btn-secondary:hover {
  background: var(--border-light);
  border-color: var(--primary);
}

.btn-back-home {
  background: var(--primary);
  color: white;
  text-decoration: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius);
  font-weight: 600;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-back-home:hover {
  background: var(--primary-dark);
  color: white;
  text-decoration: none;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Admin Panel Layout Improvements */
.admin-section {
  width: 100%;
  max-width: none;
  padding: 0;
  margin: 0 auto 2rem auto;
  display: none;
}

.admin-section.active {
  display: block;
}

/* Admin Controls and Filters */
.admin-controls {
  margin-bottom: 2rem;
}

.admin-filters {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group label {
  font-weight: 600;
  color: var(--text);
  font-size: 0.875rem;
}

.search-group {
  position: relative;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input-wrapper svg {
  position: absolute;
  left: 0.75rem;
  color: var(--text-secondary);
  pointer-events: none;
}

.admin-select,
.admin-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  font-size: 0.875rem;
  background: var(--light-background);
  color: var(--text);
  transition: all 0.2s ease;
}

.admin-input[type="search"] {
  padding-left: 2.5rem;
}

.admin-select:focus,
.admin-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* State containers */
.state-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  color: var(--text-secondary);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border);
  border-top: 3px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.spinner-small {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.admin-sidebar {
  min-width: 200px;
  max-width: 240px;
  width: 20%;
  background: var(--light-background);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  border: 1px solid var(--border);
  box-shadow: var(--shadow);
}

.admin-table th {
  background: var(--border-light);
  font-weight: 600;
  border-bottom: 2px solid var(--border);
  position: sticky;
  top: 0;
  z-index: 2;
  color: var(--text);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 1rem 0.75rem;
}

.admin-table td {
  padding: 0.75rem;
  border-bottom: 1px solid var(--border-light);
  vertical-align: top;
}

.admin-table tr:hover {
  background: var(--border-light);
}

/* Modern Admin Panel Styles */
.access-denied {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  padding: 2rem;
  background: var(--light-background);
  border-radius: var(--radius-xl);
  border: 1px solid var(--border);
  margin: 2rem auto;
  max-width: 600px;
}

.access-denied-icon {
  font-size: 4rem;
  color: var(--muted);
  margin-bottom: 1.5rem;
}

.access-denied h1 {
  color: var(--text);
  margin-bottom: 1rem;
  font-size: 2rem;
}

.access-denied p {
  color: var(--text-secondary);
  margin-bottom: 2rem;
  font-size: 1.125rem;
  line-height: 1.6;
}

.admin-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  background: var(--background);
  min-height: 100vh;
}

.admin-header {
  background: var(--light-background);
  border-radius: var(--radius-xl);
  padding: 2rem;
  margin-bottom: 2rem;
  border: 1px solid var(--border);
  box-shadow: var(--shadow);
}

.admin-header h1 {
  margin: 0 0 0.5rem 0;
  color: var(--text);
  font-size: 2.5rem;
  font-weight: 700;
}

.admin-header p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 1.125rem;
}

.admin-nav {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.admin-nav-item {
  background: var(--light-background);
  color: var(--text);
  text-decoration: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-lg);
  border: 1px solid var(--border);
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.admin-nav-item:hover {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.admin-nav-item.active {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
}

.admin-card {
  background: var(--light-background);
  border-radius: var(--radius-xl);
  padding: 2rem;
  border: 1px solid var(--border);
  box-shadow: var(--shadow);
  margin-bottom: 2rem;
}

.admin-card h2 {
  margin: 0 0 1rem 0;
  color: var(--text);
  font-size: 1.5rem;
  font-weight: 600;
}

.admin-card h3 {
  margin: 0 0 0.75rem 0;
  color: var(--text);
  font-size: 1.25rem;
  font-weight: 600;
}

.admin-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.admin-stat-card {
  background: var(--light-background);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  border: 1px solid var(--border);
  text-align: center;
  transition: all 0.2s ease;
}

.admin-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.admin-stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary);
  margin-bottom: 0.5rem;
  display: block;
}

.admin-stat-label {
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.admin-btn {
  background: var(--primary);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.admin-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.admin-btn.secondary {
  background: var(--light-background);
  color: var(--text);
  border: 1px solid var(--border);
}

.admin-btn.secondary:hover {
  background: var(--border-light);
  color: var(--text);
}

.admin-btn.danger {
  background: #dc2626;
}

.admin-btn.danger:hover {
  background: #b91c1c;
}

.admin-form {
  display: grid;
  gap: 1.5rem;
}

.admin-form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.admin-form-label {
  font-weight: 600;
  color: var(--text);
  font-size: 0.875rem;
}

.admin-form-input,
.admin-form-textarea,
.admin-form-select {
  padding: 0.75rem;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  font-size: 0.875rem;
  background: var(--light-background);
  color: var(--text);
  transition: all 0.2s ease;
}

.admin-form-input:focus,
.admin-form-textarea:focus,
.admin-form-select:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.admin-form-textarea {
  min-height: 120px;
  resize: vertical;
}

.admin-alert {
  padding: 1rem 1.5rem;
  border-radius: var(--radius);
  margin-bottom: 1.5rem;
  border: 1px solid;
}

.admin-alert.success {
  background: #f0fdf4;
  border-color: #22c55e;
  color: #15803d;
}

.admin-alert.error {
  background: #fef2f2;
  border-color: #ef4444;
  color: #dc2626;
}

.admin-alert.warning {
  background: #fffbeb;
  border-color: #f59e0b;
  color: #d97706;
}

.admin-alert.info {
  background: #eff6ff;
  border-color: #3b82f6;
  color: #2563eb;
}

/* Loading states */
.admin-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--text-secondary);
}

.admin-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border);
  border-top: 3px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Professional Form Styles */
.professional-form-wrapper {
  max-width: 1200px;
  margin: 0 auto;
}

.form-header-section {
  background: var(--light-background);
  border-radius: var(--radius-lg);
  padding: 2rem;
  margin-bottom: 2rem;
  border: 1px solid var(--border);
  box-shadow: var(--shadow);
}

.form-header-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.form-header-icon {
  background: var(--primary);
  color: white;
  padding: 1rem;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
}

.form-header-text h2 {
  margin: 0 0 0.5rem 0;
  color: var(--text);
  font-size: 1.75rem;
  font-weight: 700;
}

.form-header-text p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 1rem;
}

.form-content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.form-card {
  background: var(--light-background);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border);
  box-shadow: var(--shadow);
  overflow: hidden;
}

.form-card.primary-card {
  grid-column: 1 / -1;
}

.card-header {
  background: var(--border-light);
  padding: 1.5rem;
  border-bottom: 1px solid var(--border);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.card-icon {
  padding: 0.75rem;
  border-radius: var(--radius);
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-icon.primary-icon {
  background: rgba(37, 99, 235, 0.1);
  color: var(--primary);
}

.card-icon.secondary-icon {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.card-icon.features-icon {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.card-icon.notes-icon {
  background: rgba(139, 92, 246, 0.1);
  color: #8b5cf6;
}

.card-title h3 {
  margin: 0 0 0.25rem 0;
  color: var(--text);
  font-size: 1.25rem;
  font-weight: 600;
}

.card-title p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.card-content {
  padding: 1.5rem;
}

.input-group {
  margin-bottom: 1.5rem;
}

.input-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text);
  font-size: 0.875rem;
}

.required {
  color: #dc2626;
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  font-size: 0.875rem;
  background: var(--light-background);
  color: var(--text);
  transition: all 0.2s ease;
  font-family: inherit;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-textarea {
  min-height: 120px;
  resize: vertical;
}

.input-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.price-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.currency-prefix {
  position: absolute;
  left: 0.75rem;
  color: var(--text-secondary);
  font-weight: 600;
  pointer-events: none;
}

.price-input {
  padding-left: 2rem;
}

.input-help {
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: var(--text-secondary);
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}

.warning-help {
  color: #d97706;
}

.image-upload-area {
  position: relative;
}

.image-textarea {
  font-family: 'Courier New', monospace;
  font-size: 0.8125rem;
}

/* Features Section */
.features-display-area {
  margin-bottom: 1.5rem;
}

.keypoint-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  background: var(--border-light);
  border-radius: var(--radius);
  margin-bottom: 0.5rem;
  border: 1px solid var(--border);
}

.keypoint-label {
  font-weight: 600;
  color: var(--text);
  margin-right: 0.5rem;
}

.keypoint-value {
  color: var(--text-secondary);
  flex: 1;
}

.btn-remove-keypoint {
  background: #dc2626;
  color: white;
  border: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  line-height: 1;
  transition: all 0.2s ease;
}

.btn-remove-keypoint:hover {
  background: #b91c1c;
  transform: scale(1.1);
}

.features-input-section {
  border-top: 1px solid var(--border);
  padding-top: 1.5rem;
}

.features-input-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.add-feature-button {
  background: var(--primary);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.add-feature-button:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
}

/* Pagination */
.admin-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  margin-top: 2rem;
  padding: 1rem;
}

.page-btn {
  background: var(--light-background);
  color: var(--text);
  border: 1px solid var(--border);
  padding: 0.5rem 0.75rem;
  border-radius: var(--radius);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  min-width: 40px;
  text-align: center;
}

.page-btn:hover {
  background: var(--border-light);
  border-color: var(--primary);
}

.page-btn.active {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
}

/* GitHub Status */
.github-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--border-light);
  border-radius: var(--radius);
  border: 1px solid var(--border);
}

.status-indicator {
  font-size: 0.75rem;
}

.status-text {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Categories Grid */
.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.category-card {
  background: var(--light-background);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.category-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.category-info h4 {
  margin: 0 0 0.5rem 0;
  color: var(--text);
  font-size: 1.125rem;
  font-weight: 600;
}

.category-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
}

.btn-edit-category,
.btn-delete-category {
  padding: 0.375rem;
  border: none;
  border-radius: var(--radius);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-edit-category {
  background: var(--primary);
  color: white;
}

.btn-edit-category:hover {
  background: var(--primary-dark);
}

.btn-delete-category {
  background: #dc2626;
  color: white;
}

.btn-delete-category:hover {
  background: #b91c1c;
}

.no-categories {
  text-align: center;
  color: var(--text-secondary);
  font-style: italic;
  padding: 2rem;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  align-items: center;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border);
}

.form-header h2 {
  margin: 0;
  color: var(--text);
  font-size: 1.5rem;
  font-weight: 600;
}

/* Stat Cards */
.stat-card {
  background: var(--light-background);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  text-align: center;
  transition: all 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary);
  margin-bottom: 0.5rem;
}

.stat-label {
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Admin Products Table */
.admin-table-wrapper {
  background: var(--light-background);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border);
  overflow: hidden;
  box-shadow: var(--shadow);
}

.admin-table,
.admin-products-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

/* Product table specific styles */
.product-row {
  transition: background-color 0.2s ease;
}

.product-row:hover {
  background: var(--border-light);
}

.product-row.product-new {
  background: rgba(34, 197, 94, 0.1);
  border-left: 3px solid #22c55e;
}

.product-row.product-modified {
  background: rgba(245, 158, 11, 0.1);
  border-left: 3px solid #f59e0b;
}

.product-name-cell {
  max-width: 300px;
}

.product-name-container {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.product-description-preview {
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1.3;
}

.change-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.change-indicator.new {
  color: #22c55e;
}

.change-indicator.modified {
  color: #f59e0b;
}

.category-badge {
  background: var(--border-light);
  color: var(--text-secondary);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius);
  font-size: 0.75rem;
  font-weight: 500;
}

.images-count {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.status-indicators {
  display: flex;
  gap: 0.25rem;
}

.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.good {
  background: #f0fdf4;
  color: #15803d;
  border: 1px solid #22c55e;
}

.status-badge.defects {
  background: #fffbeb;
  color: #d97706;
  border: 1px solid #f59e0b;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.btn-edit,
.btn-delete {
  padding: 0.375rem;
  border: none;
  border-radius: var(--radius);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-edit {
  background: var(--primary);
  color: white;
}

.btn-edit:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
}

.btn-delete {
  background: #dc2626;
  color: white;
}

.btn-delete:hover {
  background: #b91c1c;
  transform: translateY(-1px);
}

.admin-table thead {
  background: var(--border-light);
}

.admin-table th {
  padding: 1rem 0.75rem;
  text-align: left;
  font-weight: 600;
  color: var(--text);
  border-bottom: 1px solid var(--border);
  position: sticky;
  top: 0;
  z-index: 10;
}

.admin-table td {
  padding: 0.75rem;
  border-bottom: 1px solid var(--border-light);
  vertical-align: top;
}

.admin-table tr:hover {
  background: var(--border-light);
}

.admin-table tr:last-child td {
  border-bottom: none;
}

.admin-table-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.admin-table-btn {
  padding: 0.375rem 0.75rem;
  border: none;
  border-radius: var(--radius);
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.admin-table-btn.edit {
  background: var(--primary);
  color: white;
}

.admin-table-btn.edit:hover {
  background: var(--primary-dark);
}

.admin-table-btn.delete {
  background: #dc2626;
  color: white;
}

.admin-table-btn.delete:hover {
  background: #b91c1c;
}

.admin-table-btn.view {
  background: var(--border-light);
  color: var(--text);
  border: 1px solid var(--border);
}

.admin-table-btn.view:hover {
  background: var(--border);
}

.product-image-thumb {
  width: 60px;
  height: 45px;
  object-fit: cover;
  border-radius: var(--radius);
  border: 1px solid var(--border);
}

.product-status {
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.product-status.available {
  background: #f0fdf4;
  color: #15803d;
  border: 1px solid #22c55e;
}

.product-status.sold {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #ef4444;
}

.product-status.pending {
  background: #fffbeb;
  color: #d97706;
  border: 1px solid #f59e0b;
}

/* Categories Management */
.categories-container {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 2rem;
  align-items: start;
}

.category-form {
  background: var(--light-background);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  border: 1px solid var(--border);
  box-shadow: var(--shadow);
}

.categories-list {
  background: var(--light-background);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border);
  box-shadow: var(--shadow);
  overflow: hidden;
}

.category-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-light);
}

.category-item:last-child {
  border-bottom: none;
}

.category-item:hover {
  background: var(--border-light);
}

.category-name {
  font-weight: 500;
  color: var(--text);
}

.category-count {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-left: 0.5rem;
}

.category-actions {
  display: flex;
  gap: 0.5rem;
}

/* Mobile Responsive for Admin Panel */
@media (max-width: 768px) {
  .admin-container {
    padding: 1rem;
  }

  .admin-header {
    padding: 1.5rem;
  }

  .admin-header h1 {
    font-size: 2rem;
  }

  .admin-nav {
    flex-direction: column;
    gap: 0.5rem;
  }

  .admin-nav-item {
    justify-content: center;
  }

  .admin-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .admin-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .admin-btn {
    justify-content: center;
  }

  .categories-container {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .admin-table-wrapper {
    overflow-x: auto;
  }

  .admin-table {
    min-width: 600px;
  }

  .admin-table th,
  .admin-table td {
    padding: 0.5rem;
    font-size: 0.75rem;
  }

  .admin-table-actions {
    flex-direction: column;
    gap: 0.25rem;
  }

  .admin-table-btn {
    font-size: 0.625rem;
    padding: 0.25rem 0.5rem;
  }

  .product-image-thumb {
    width: 40px;
    height: 30px;
  }
}
