---
// Add to <PERSON>t <PERSON>ton Component - Refactored with smaller components
import QuantitySelector from './cart/QuantitySelector.astro';
import CartButton from './cart/CartButton.astro';

export interface Props {
  productId: string;
  productName: string;
  productPrice: number;
  productImage?: string;
  productSlug: string;
  productCategory: string;
  productDescription?: string;
  productKeyPoints?: any[];
  maxQuantity?: number;
  size?: 'small' | 'medium' | 'large';
  variant?: 'primary' | 'secondary' | 'outline';
  showQuantity?: boolean;
  fullWidth?: boolean;
}

const {
  productId,
  productName,
  productPrice,
  productImage,
  productSlug,
  productCategory,
  productDescription,
  productKeyPoints,
  maxQuantity = 1,
  size = 'medium',
  variant = 'primary',
  showQuantity = false,
  fullWidth = false
} = Astro.props;
---

<div class={`add-to-cart-container ${size}`} data-product-id={productId}>
  {showQuantity && (
    <QuantitySelector 
      productId={productId}
      maxQuantity={maxQuantity}
      size={size}
      showByDefault={false}
    />
  )}
  
  <CartButton
    productId={productId}
    productName={productName}
    productPrice={productPrice}
    productImage={productImage}
    productSlug={productSlug}
    productCategory={productCategory}
    productDescription={productDescription}
    productKeyPoints={productKeyPoints}
    maxQuantity={maxQuantity}
    size={size}
    variant={variant}
    fullWidth={fullWidth}
  />
</div>

<!-- Load external cart button manager script -->
<script src="/scripts/cart-button-manager.js" is:inline></script>

<style>
  .add-to-cart-container {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }

  .add-to-cart-container.small {
    gap: 0.5rem;
  }

  .add-to-cart-container.large {
    gap: 1rem;
  }

  /* Responsive layout */
  @media (min-width: 640px) {
    .add-to-cart-container {
      flex-direction: row;
      align-items: center;
    }
  }
</style>
