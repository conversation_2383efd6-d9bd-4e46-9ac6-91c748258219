---
// Cart state management for Snipcart integration
// Handles visual feedback and button state updates
---

<!-- Load external cart state manager script -->
<script src="/scripts/cart-state-manager.js" is:inline></script>

<style is:global>
  /* Button state styles */
  .snipcart-add-item.btn-loading {
    background: #6b7280 !important;
    cursor: not-allowed !important;
  }

  .snipcart-add-item.btn-added {
    background: #10b981 !important;
    cursor: not-allowed !important;
  }

  .snipcart-add-item.btn-available {
    background: var(--primary) !important;
    cursor: pointer !important;
  }

  .snipcart-add-item.btn-available:hover {
    background: var(--primary-dark) !important;
  }

  /* Cart icon animation */
  .cart-trigger.cart-bounce {
    animation: cartBounce 0.6s ease-in-out;
  }

  @keyframes cartBounce {
    0%, 20%, 60%, 100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-10px);
    }
    80% {
      transform: translateY(-5px);
    }
  }

  /* Spin animation for loading state */
  .animate-spin {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  /* Ensure disabled buttons look disabled */
  .snipcart-add-item:disabled {
    opacity: 0.8;
    transform: none !important;
  }

  .snipcart-add-item:disabled:hover {
    transform: none !important;
  }
</style>
