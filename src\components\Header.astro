---
// Mobile-optimized header with hamburger menu
import <PERSON><PERSON><PERSON><PERSON><PERSON> from './Snipcart.astro';
---
<header class="site-header">
  <div class="container header-flex">
    <a href="/" class="logo">
      <svg width="40" height="40" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg" aria-label="Cheers Marketplace Logo">
        <path d="M50.4 78.5a75.1 75.1 0 0 0-28.5 6.9l24.2-65.7c.7-2 1.9-3.2 3.4-3.2h29c1.5 0 2.7 1.2 3.4 3.2l24.2 65.7s-11.6-7-28.5-7L67 45.5c-.4-1.7-1.6-2.8-2.9-2.8-1.3 0-2.5 1.1-2.9 2.7L50.4 78.5Zm-1.1 28.2Zm-4.2-20.2c-2 6.6-.6 15.8 4.2 20.2a17.5 17.5 0 0 1 .2-.7 5.5 5.5 0 0 1 5.7-4.5c2.8.1 4.3 1.5 4.7 4.7.2 1.1.2 2.3.2 3.5v.4c0 2.7.7 5.2 2.2 7.4a13 13 0 0 0 5.7 4.9v-.3l-.2-.3c-1.8-5.6-.5-9.5 4.4-12.8l1.5-1a73 73 0 0 0 3.2-2.2 16 16 0 0 0 6.8-11.4c.3-2 .1-4-.6-6l-.8.6-1.6 1a37 37 0 0 1-22.4 2.7c-5-.7-9.7-2-13.2-6.2Z" fill="currentColor" />
      </svg>
      <span class="logo-text">Cheers Marketplace</span>
    </a>

    <!-- Mobile hamburger button -->
    <button class="mobile-menu-toggle" aria-label="Toggle navigation menu" aria-expanded="false">
      <span class="hamburger-line"></span>
      <span class="hamburger-line"></span>
      <span class="hamburger-line"></span>
    </button>

    <!-- Navigation menu -->
    <nav class="main-nav" id="main-nav">
      <a href="/">Home</a>
      <a href="/products">Products</a>
      <a href="/about">About</a>
      <a href="/faq">FAQ</a>
    </nav>

    <!-- Shopping Cart -->
    <div class="header-cart">
      <Snipcart />
    </div>
  </div>
</header>

<!-- Mobile menu functionality is now handled by the optimized core bundle -->
<script is:inline>
  // Ensure mobile menu is initialized when needed
  function ensureMobileMenuReady() {
    // Mobile menu is now part of the core bundle, just ensure it's initialized
    if (window.CheersMPCore?.menu && !window.CheersMPCore.menu.isInitialized) {
      window.CheersMPCore.menu.init();
    }
  }

  // Initialize mobile menu when core bundle is ready
  if (window.CheersMPCore) {
    ensureMobileMenuReady();
  } else {
    // Wait for core bundle to load
    document.addEventListener('DOMContentLoaded', () => {
      if (window.CheersMPCore) {
        ensureMobileMenuReady();
      }
    });
  }
</script>

<style>
  /* Logo SVG styling to match favicon behavior */
  .logo svg {
    color: #000;
    transition: color 0.2s ease;
  }

  @media (prefers-color-scheme: dark) {
    .logo svg {
      color: #FFF;
    }
  }

  .logo:hover svg {
    opacity: 0.8;
  }

  /* Header cart positioning */
  .header-cart {
    margin-left: auto;
    display: flex;
    align-items: center;
  }

  /* Ensure header flex layout accommodates cart */
  .header-flex {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
  }

  /* Mobile adjustments for cart */
  @media (max-width: 768px) {
    .header-cart {
      order: 2;
      margin-left: 0;
    }

    .mobile-menu-toggle {
      order: 3;
    }

    .main-nav {
      order: 4;
    }
  }
</style>
