---
// Optimized Image Component
// Handles both local images (with Astro optimization) and external CDN images (Bunny CDN ready)

import { Image } from 'astro:assets';

export interface Props {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  sizes?: string;
  loading?: 'lazy' | 'eager';
  fetchpriority?: 'high' | 'low' | 'auto';
  class?: string;
  style?: string;
  quality?: number;
  format?: 'webp' | 'avif' | 'png' | 'jpg' | 'jpeg';
  // CDN-specific props
  cdnOptimization?: boolean;
  cdnTransforms?: string;
  fallbackSrc?: string;
}

const {
  src,
  alt,
  width = 800,
  height = 600,
  sizes = '(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw',
  loading = 'lazy',
  fetchpriority = 'auto',
  class: className = '',
  style = '',
  quality = 80,
  format = 'webp',
  cdnOptimization = false,
  cdnTransforms = '',
  fallbackSrc
} = Astro.props;

// Check if image is external (CDN) or local
const isExternal = src.startsWith('http') || src.startsWith('//');
const isLocalAsset = !isExternal && (src.startsWith('/src/') || src.startsWith('./') || src.startsWith('../'));

// Generate responsive image URLs for CDN
function generateCDNSizes(baseSrc: string, transforms: string = ''): string | { small: string; medium: string; large: string; xlarge: string } {
  if (!cdnOptimization) return baseSrc;

  // Bunny CDN optimization parameters
  // Format: ?width=X&height=Y&quality=Z&format=webp
  const baseParams = transforms || `quality=${quality}&format=${format}`;

  return {
    small: `${baseSrc}?width=480&${baseParams}`,
    medium: `${baseSrc}?width=768&${baseParams}`,
    large: `${baseSrc}?width=1200&${baseParams}`,
    xlarge: `${baseSrc}?width=1600&${baseParams}`
  };
}

// Generate srcset for CDN images
function generateCDNSrcSet(baseSrc: string, transforms: string = '') {
  if (!cdnOptimization) return undefined;

  const sizes = generateCDNSizes(baseSrc, transforms);
  if (typeof sizes === 'string') return undefined;
  return `${sizes.small} 480w, ${sizes.medium} 768w, ${sizes.large} 1200w, ${sizes.xlarge} 1600w`;
}

const cdnSizes = isExternal ? generateCDNSizes(src, cdnTransforms) : null;
const cdnSrcSet = isExternal ? generateCDNSrcSet(src, cdnTransforms) : null;

// Type guard for CDN sizes
const isValidCDNSizes = (sizes: any): sizes is { small: string; medium: string; large: string; xlarge: string } => {
  return sizes && typeof sizes === 'object' && 'large' in sizes;
};
---

{isLocalAsset ? (
  <!-- Local images: Use Astro's built-in optimization -->
  <Image
    src={src}
    alt={alt}
    width={width}
    height={height}
    quality={quality}
    format={format}
    loading={loading}
    fetchpriority={fetchpriority}
    sizes={sizes}
    class={className}
    style={style}
  />
) : isExternal && cdnOptimization ? (
  <!-- External CDN images: Use responsive srcset with CDN optimization -->
  <img
    src={isValidCDNSizes(cdnSizes) ? cdnSizes.large : src}
    srcset={cdnSrcSet}
    sizes={sizes}
    alt={alt}
    width={width}
    height={height}
    loading={loading}
    fetchpriority={fetchpriority}
    class={className}
    style={style}
    decoding="async"
    onerror={fallbackSrc ? `this.src='${fallbackSrc}'` : undefined}
  />
) : (
  <!-- External images without CDN optimization: Standard img tag -->
  <img
    src={src}
    alt={alt}
    width={width}
    height={height}
    loading={loading}
    fetchpriority={fetchpriority}
    sizes={sizes}
    class={className}
    style={style}
    decoding="async"
    onerror={fallbackSrc ? `this.src='${fallbackSrc}'` : undefined}
  />
)}

<style>
  img {
    max-width: 100%;
    height: auto;
    display: block;
  }
  
  /* Aspect ratio preservation */
  img[width][height] {
    aspect-ratio: attr(width) / attr(height);
  }
  
  /* Loading state */
  img[loading="lazy"] {
    content-visibility: auto;
  }
  
  /* Error state styling */
  img:not([src]),
  img[src=""] {
    background: var(--border-light);
    border: 1px solid var(--border);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    font-size: 0.875rem;
  }
  
  img:not([src])::before,
  img[src=""]::before {
    content: "Image not available";
  }
</style>

<!-- 
Usage Examples:

1. Local images (Astro optimized):
<OptimizedImage 
  src="/src/assets/images/hero.jpg"
  alt="Hero image"
  width={1200}
  height={600}
  loading="eager"
  fetchpriority="high"
/>

2. External CDN images (Bunny CDN ready):
<OptimizedImage 
  src="https://cdn.example.com/product.jpg"
  alt="Product image"
  width={800}
  height={600}
  cdnOptimization={true}
  cdnTransforms="quality=85&format=webp"
  fallbackSrc="/images/placeholder.jpg"
/>

3. External images (standard):
<OptimizedImage 
  src="https://images.unsplash.com/photo-123"
  alt="Unsplash image"
  width={600}
  height={400}
/>
-->
