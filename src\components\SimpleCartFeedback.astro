---
// Cart feedback functionality is now integrated into the optimized core bundle
// This component serves as a placeholder for backward compatibility
---

<!-- Cart feedback is now handled by the core bundle - no additional script needed -->
<script is:inline>
  // Ensure cart feedback is ready (handled by core bundle)
  if (window.CheersMPCore?.cart) {
    console.log('✓ Cart feedback ready via core bundle');
  } else {
    console.warn('Core bundle not loaded yet - cart feedback may be delayed');
  }
</script>

<style is:global>
  /* Simple cart feedback styles */
  .snipcart-add-item.btn-loading {
    background: #6b7280 !important;
    cursor: not-allowed !important;
  }

  .snipcart-add-item.btn-added {
    background: #10b981 !important;
    cursor: not-allowed !important;
  }

  .snipcart-add-item.btn-available {
    background: var(--primary) !important;
    cursor: pointer !important;
  }

  .snipcart-add-item.btn-available:hover {
    background: var(--primary-dark) !important;
  }

  .snipcart-add-item:disabled {
    opacity: 0.8;
    transform: none !important;
  }

  .snipcart-add-item:disabled:hover {
    transform: none !important;
  }

  .cart-trigger.cart-bounce {
    animation: cartBounce 0.6s ease-in-out;
  }

  @keyframes cartBounce {
    0%, 20%, 60%, 100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-10px);
    }
    80% {
      transform: translateY(-5px);
    }
  }

  .animate-spin {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
</style>
