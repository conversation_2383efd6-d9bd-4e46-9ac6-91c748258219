---
// Snipcart cart button component for header
// The main Snipcart script is loaded in Layout.astro
---

<!-- Cart trigger button for header -->
<button 
  class="snipcart-checkout cart-trigger"
  type="button"
  aria-label="Open shopping cart"
>
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
    <circle cx="9" cy="21" r="1"></circle>
    <circle cx="20" cy="21" r="1"></circle>
    <path d="m1 1 4 4 2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
  </svg>
  <span class="cart-count snipcart-items-count"></span>
</button>

<style>
  .cart-trigger {
    position: relative;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    color: var(--text);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .cart-trigger:hover {
    background: var(--light-background);
    color: var(--primary);
  }

  .cart-count {
    position: absolute;
    top: 0;
    right: 0;
    background: var(--primary);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: translate(25%, -25%);
    min-width: 20px;
  }

  .cart-count:empty {
    display: none;
  }

  /* Custom Snipcart styling */
  :global(.snipcart-modal__container) {
    z-index: 9999;
  }

  :global(.snipcart-layout) {
    font-family: var(--font-family);
  }

  :global(.snipcart__box--badge-highlight) {
    background-color: var(--primary) !important;
  }

  :global(.snipcart__button--primary) {
    background-color: var(--primary) !important;
    border-color: var(--primary) !important;
  }

  :global(.snipcart__button--primary:hover) {
    background-color: var(--primary-dark) !important;
    border-color: var(--primary-dark) !important;
  }
</style>
