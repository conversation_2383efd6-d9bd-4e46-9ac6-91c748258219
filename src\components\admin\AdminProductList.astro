---
// Admin Product List Component - Product table and pagination
---

<div id="section-list" class="admin-section active">
  <!-- Loading and Empty States -->
  <div id="loading-state" class="state-container" style="display: none;">
    <div class="loading-spinner"></div>
    <p>Loading products...</p>
  </div>
  
  <div id="empty-state" class="state-container" style="display: none;">
    <svg width="64" height="64" viewBox="0 0 24 24" fill="currentColor">
      <path d="M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z"/>
    </svg>
    <h3>No products found</h3>
    <p>Start by adding your first product to the catalog.</p>
    <button class="btn-primary" onclick="document.getElementById('tab-form').click()">
      Add Your First Product
    </button>
  </div>

  <!-- Products Table -->
  <div class="admin-table-wrapper">
    <table id="products-table" class="admin-products-table">
      <thead>
        <tr>
          <th>Name</th>
          <th>Category</th>
          <th>Price</th>
          <th>Images</th>
          <th>Status</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody id="products-tbody">
        <!-- Products will be loaded here -->
      </tbody>
    </table>
  </div>

  <!-- Pagination -->
  <div id="pagination" class="admin-pagination">
    <!-- Pagination will be loaded here -->
  </div>
</div>
