---
// <PERSON><PERSON> Component
export interface Props {
  productId: string;
  productName: string;
  productPrice: number;
  productImage?: string;
  productSlug: string;
  productCategory: string;
  productDescription?: string;
  productKeyPoints?: any[];
  maxQuantity?: number;
  size?: 'small' | 'medium' | 'large';
  variant?: 'primary' | 'secondary' | 'outline';
  fullWidth?: boolean;
}

const {
  productId,
  productName,
  productPrice,
  productImage,
  productSlug,
  productCategory,
  productDescription,
  productKeyPoints,
  maxQuantity = 1,
  size = 'medium',
  variant = 'primary',
  fullWidth = false
} = Astro.props;

// Format price for display
const formattedPrice = `$${productPrice.toFixed(2)}`;
---

<button
  class={`cart-btn ${variant} ${size} ${fullWidth ? 'full-width' : ''} snipcart-add-item`}
  data-item-id={productId}
  data-item-name={productName}
  data-item-price={productPrice}
  data-item-image={productImage || ''}
  data-item-url={`/products/${productSlug}`}
  data-item-description={productDescription || ''}
  data-item-categories={productCategory}
  data-item-custom1-name="Key Points"
  data-item-custom1-value={JSON.stringify(productKeyPoints || [])}
  data-item-max-quantity={maxQuantity}
  aria-label={`Add ${productName} to cart for ${formattedPrice}`}
>
  <svg class="cart-icon" width="20" height="20" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
    <path d="M11,9H13V6H16V4H13V1H11V4H8V6H11M7,18C5.9,18 5,18.9 5,20S5.9,22 7,22 9,20.1 9,20 8.1,18 7,18M1,2V4H3L6.6,11.59L5.24,14.04C5.09,14.32 5,14.65 5,15A2,2 0 0,0 7,17H19V15H7.42A0.25,0.25 0 0,1 7.17,14.75C7.17,14.7 7.18,14.66 7.2,14.63L8.1,13H15.55C16.3,13 16.96,12.58 17.3,11.97L20.88,5H5.21L4.27,3H1M17,18C15.9,18 15,18.9 15,20S15.9,22 17,22 19,20.1 19,20 18.1,18 17,18Z"/>
  </svg>
  <span class="btn-text">Add to Cart - {formattedPrice}</span>
  <span class="btn-text-loading" style="display: none;">
    <svg class="loading-spinner" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <path d="M21 12a9 9 0 11-6.219-8.56"/>
    </svg>
    Adding...
  </span>
  <span class="btn-text-added" style="display: none;">
    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
      <path d="M9 12l2 2 4-4"/>
      <circle cx="12" cy="12" r="10"/>
    </svg>
    Added!
  </span>
</button>

<style>
  .cart-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--radius);
    font-weight: 600;
    font-size: 1rem;
    line-height: 1;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
  }

  .cart-btn.full-width {
    width: 100%;
  }

  /* Size variants */
  .cart-btn.small {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    gap: 0.375rem;
  }

  .cart-btn.small .cart-icon {
    width: 16px;
    height: 16px;
  }

  .cart-btn.large {
    padding: 1rem 2rem;
    font-size: 1.125rem;
    gap: 0.75rem;
  }

  .cart-btn.large .cart-icon {
    width: 24px;
    height: 24px;
  }

  /* Color variants */
  .cart-btn.primary {
    background: var(--primary);
    color: white;
  }

  .cart-btn.primary:hover:not(:disabled) {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }

  .cart-btn.secondary {
    background: var(--light-background);
    color: var(--text);
    border: 1px solid var(--border);
  }

  .cart-btn.secondary:hover:not(:disabled) {
    background: var(--border-light);
    border-color: var(--primary);
    transform: translateY(-1px);
  }

  .cart-btn.outline {
    background: transparent;
    color: var(--primary);
    border: 2px solid var(--primary);
  }

  .cart-btn.outline:hover:not(:disabled) {
    background: var(--primary);
    color: white;
    transform: translateY(-1px);
  }

  /* States */
  .cart-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
  }

  .cart-btn.loading {
    pointer-events: none;
  }

  .cart-btn.added {
    background: var(--success) !important;
    color: white !important;
  }

  /* Loading spinner animation */
  .loading-spinner {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  /* Icon styling */
  .cart-icon {
    flex-shrink: 0;
  }

  /* Text states */
  .btn-text,
  .btn-text-loading,
  .btn-text-added {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  /* Responsive adjustments */
  @media (max-width: 640px) {
    .cart-btn {
      padding: 0.625rem 1.25rem;
      font-size: 0.9375rem;
    }

    .cart-btn.large {
      padding: 0.75rem 1.5rem;
      font-size: 1rem;
    }
  }
</style>
