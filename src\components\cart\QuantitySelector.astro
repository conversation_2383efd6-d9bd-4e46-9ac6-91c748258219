---
// Quantity Selector Component
export interface Props {
  productId: string;
  maxQuantity?: number;
  initialQuantity?: number;
  size?: 'small' | 'medium' | 'large';
  showByDefault?: boolean;
}

const {
  productId,
  maxQuantity = 1,
  initialQuantity = 1,
  size = 'medium',
  showByDefault = false
} = Astro.props;
---

<div 
  class={`quantity-selector ${size}`} 
  data-product-id={productId}
  data-max-quantity={maxQuantity}
  style={showByDefault ? '' : 'display: none;'}
>
  <button 
    class="qty-btn qty-decrease" 
    aria-label="Decrease quantity"
    disabled={initialQuantity <= 1}
  >
    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
      <path d="M19,13H5V11H19V13Z"/>
    </svg>
  </button>
  
  <span class="quantity-display" data-quantity={initialQuantity}>
    {initialQuantity}
  </span>
  
  <button 
    class="qty-btn qty-increase" 
    aria-label="Increase quantity"
    disabled={initialQuantity >= maxQuantity}
  >
    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
      <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
    </svg>
  </button>
</div>

<style>
  .quantity-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--light-background);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    padding: 0.25rem;
  }

  .quantity-selector.small {
    gap: 0.25rem;
    padding: 0.125rem;
  }

  .quantity-selector.large {
    gap: 0.75rem;
    padding: 0.5rem;
  }

  .qty-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: none;
    background: var(--primary);
    color: white;
    border-radius: var(--radius);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1rem;
    line-height: 1;
  }

  .qty-btn:hover:not(:disabled) {
    background: var(--primary-dark);
    transform: translateY(-1px);
  }

  .qty-btn:disabled {
    background: var(--border);
    color: var(--text-secondary);
    cursor: not-allowed;
    transform: none;
  }

  .quantity-selector.small .qty-btn {
    width: 24px;
    height: 24px;
    font-size: 0.875rem;
  }

  .quantity-selector.large .qty-btn {
    width: 40px;
    height: 40px;
    font-size: 1.125rem;
  }

  .quantity-display {
    min-width: 2rem;
    text-align: center;
    font-weight: 600;
    color: var(--text);
    font-size: 1rem;
  }

  .quantity-selector.small .quantity-display {
    min-width: 1.5rem;
    font-size: 0.875rem;
  }

  .quantity-selector.large .quantity-display {
    min-width: 2.5rem;
    font-size: 1.125rem;
  }
</style>
