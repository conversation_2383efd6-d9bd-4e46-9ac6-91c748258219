---
import GoogleAnalytics from '../components/GoogleAnalytics.astro';
import ToastNotification from '../components/ToastNotification.astro';
import SimpleCartFeedback from '../components/SimpleCartFeedback.astro';
import OrganizationSchema from '../components/seo/OrganizationSchema.astro';
import '../assets/global.css';

export interface Props {
	title: string;
	description?: string;
	image?: string;
	noIndex?: boolean;
}

const { title, description = "Cheers Marketplace - Quality secondhand goods in Chico, CA", image = "/cheers-marketplace-og.jpg", noIndex = false } = Astro.props;

const canonicalURL = new URL(Astro.url.pathname, Astro.site);
---

<!doctype html>
<html lang="en">
<head>
	<meta charset="UTF-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
	<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
	<meta name="generator" content={Astro.generator} />

	<!-- Performance optimizations for mobile -->
	<meta name="format-detection" content="telephone=no" />
	<meta name="mobile-web-app-capable" content="yes" />
	<meta name="apple-mobile-web-app-capable" content="yes" />
	<meta name="apple-mobile-web-app-status-bar-style" content="default" />

	<!-- Optimized font loading strategy - using system fonts for maximum performance -->
	<!-- System fonts provide instant loading with no external requests -->

	<!-- Preconnect to most critical domains (full connection setup) -->
	<link rel="preconnect" href="https://cdn.snipcart.com" crossorigin />
	<link rel="preconnect" href="https://www.googletagmanager.com" />

	<!-- Resource Hints for Performance Optimization -->
	<link rel="preconnect" href="https://images.unsplash.com" />
	<link rel="preconnect" href="https://cdn.snipcart.com" />
	<link rel="dns-prefetch" href="https://www.googletagmanager.com" />

	<!-- Preload Critical Resources -->
	<link rel="preload" href="/scripts/core-bundle.js" as="script" />

	<!-- Optimized Font Loading Strategy -->
	<link rel="preconnect" href="https://fonts.googleapis.com" />
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

	<!-- Critical font preload for immediate text rendering -->
	<link rel="preload" href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&display=swap" as="style" />

	<!-- Async font loading with fallback -->
	<style>
		/* Font loading optimization with system font fallback */
		@font-face {
			font-family: 'Playfair Display Fallback';
			src: local('Georgia'), local('Times New Roman'), local('serif');
			font-weight: 400 700;
			font-display: swap;
		}
	</style>

	<!-- Load Google Fonts asynchronously -->
	<script is:inline>
		// Optimized font loading with performance monitoring
		(function() {
			const fontLink = document.createElement('link');
			fontLink.rel = 'stylesheet';
			fontLink.href = 'https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&display=swap';

			// Load font asynchronously
			fontLink.media = 'print';
			fontLink.onload = function() {
				fontLink.media = 'all';
				// Font loaded successfully
				document.documentElement.classList.add('fonts-loaded');
			};

			// Fallback timeout
			setTimeout(function() {
				if (!document.documentElement.classList.contains('fonts-loaded')) {
					document.documentElement.classList.add('fonts-timeout');
				}
			}, 3000);

			document.head.appendChild(fontLink);
		})();
	</script>

	<!-- Critical CSS inlined for instant rendering -->
	<style>
		/* Critical above-the-fold styles - optimized for performance */
		:root{--primary:#2563eb;--primary-dark:#1d4ed8;--primary-light:#3b82f6;--secondary:#1e293b;--background:#f8fafc;--light-background:#ffffff;--card-bg:#ffffff;--text:#0f172a;--text-secondary:#475569;--border:#e2e8f0;--radius:0.5rem;--radius-lg:0.75rem;--radius-xl:1rem;--container-width:1200px;--shadow:0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);--shadow-lg:0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);--font-system:system-ui,-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,sans-serif;--font-serif:"Playfair Display Fallback",Georgia,serif}

		/* Font loading states for progressive enhancement */
		.fonts-loaded{--font-serif:"Playfair Display",Georgia,serif}
		.fonts-timeout{--font-serif:Georgia,serif}
		*,*::before,*::after{box-sizing:border-box}
		html,body{margin:0;padding:0;font-family:var(--font-system);background:var(--background);color:var(--text);line-height:1.6;font-size:16px;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}
		.container{max-width:var(--container-width);margin:0 auto;padding:0 2rem}
		h1,h2,h3{font-family:var(--font-serif);color:var(--text);font-weight:600;line-height:1.2;margin:0 0 1rem 0}
		h1{font-size:2.5rem}h2{font-size:2rem}h3{font-size:1.5rem}
		.site-header{background:var(--light-background);border-bottom:1px solid var(--border);position:sticky;top:0;z-index:100;backdrop-filter:blur(10px)}
		.header-flex{display:flex;align-items:center;justify-content:space-between;padding:1rem 2rem;min-height:70px}
		.logo{display:flex;align-items:center;gap:0.75rem;text-decoration:none;color:var(--text);font-weight:600;font-size:1.25rem}
		.main-nav{display:flex;gap:2rem;align-items:center}
		.main-nav a{text-decoration:none;color:var(--text-secondary);font-weight:500;transition:color 0.2s ease}
		.main-nav a:hover{color:var(--primary)}
		.btn{display:inline-flex;align-items:center;justify-content:center;padding:0.75rem 1.5rem;border:none;border-radius:var(--radius);font-weight:500;text-decoration:none;transition:all 0.2s ease;cursor:pointer;font-size:0.875rem}
		.btn.primary{background:var(--primary);color:white}
		.btn.primary:hover{background:var(--primary-dark)}
		.cart-trigger{position:relative;background:none;border:none;padding:0.5rem;cursor:pointer;color:var(--text-secondary);transition:color 0.2s ease}
		.cart-trigger:hover{color:var(--primary)}
		.cart-count{position:absolute;top:-5px;right:-5px;background:var(--primary);color:white;border-radius:50%;width:20px;height:20px;font-size:0.75rem;display:flex;align-items:center;justify-content:center;font-weight:600}
		.mobile-menu-toggle{display:none;flex-direction:column;gap:4px;background:none;border:none;padding:0.5rem;cursor:pointer}
		.hamburger-line{width:24px;height:2px;background:var(--text);transition:all 0.3s ease}
		@media (max-width: 768px){.main-nav{display:none}.mobile-menu-toggle{display:flex}.container{padding:0 1rem}.header-flex{padding:1rem}h1{font-size:2rem}h2{font-size:1.75rem}h3{font-size:1.25rem}}
		.loading{opacity:0.6;pointer-events:none}
		.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0, 0, 0, 0);white-space:nowrap;border:0}
		*:focus-visible{outline:2px solid var(--primary);outline-offset:2px}
	</style>

	<!-- CSS optimization complete - page-specific CSS is already loaded by Astro -->



	<!-- Dynamic meta tags -->
	<title>{title}</title>
	<meta name="description" content={description} />
	<meta name="keywords" content="cheap used goods, affordable secondhand, quality thrift, budget items, Chico CA, family business, pre-owned, discount goods, bargain finds, low cost, inexpensive, economical, value shopping" />
	<meta name="author" content="Cheers Marketplace" />
	<meta name="robots" content={noIndex ? "noindex, nofollow" : "index, follow"} />
	<link rel="canonical" href={canonicalURL} />

	<!-- Open Graph / Facebook -->
	<meta property="og:type" content="website" />
	<meta property="og:url" content={canonicalURL} />
	<meta property="og:title" content={title} />
	<meta property="og:description" content={description} />
	<meta property="og:image" content={new URL(image, Astro.site)} />

	<!-- Twitter -->
	<meta name="twitter:card" content="summary_large_image" />
	<meta name="twitter:url" content={canonicalURL} />
	<meta name="twitter:title" content={title} />
	<meta name="twitter:description" content={description} />
	<meta name="twitter:image" content={new URL(image, Astro.site)} />

	<!-- Google Analytics -->
	<GoogleAnalytics measurementId="G-5FH6Y2MPJN" />

	<!-- Organization Structured Data -->
	<OrganizationSchema />



	<!-- Page-specific head content -->
	<slot name="head" />
</head>
	<body>
		<slot />

		<!-- Toast Notifications -->
		<ToastNotification />

		<!-- Simple Cart Feedback -->
		<SimpleCartFeedback />
		<!-- Reliable Snipcart Integration -->
		<script define:vars={{ snipcartPublicKey: import.meta.env.PUBLIC_SNIPCART_API_KEY || 'YTY0ZTIxNjYtNDVlMC00NmUyLWFkNjItYTg3ZmNhMTc4MDQyNjM4ODczNzI4NTM0MTY0NDA4' }} is:inline>
			// Set API key for scripts
			window.SNIPCART_API_KEY = snipcartPublicKey;

			// Define Snipcart settings BEFORE any scripts load
			window.SnipcartSettings = {
				publicApiKey: snipcartPublicKey,
				loadStrategy: "on-user-interaction",
				currency: "usd",
				addProductBehavior: "none",
				modalStyle: "side"
			};
		</script>

		<!-- Optimized JavaScript Bundle Loading - Performance Enhanced -->
		<script is:inline>
			// Ultra-efficient script loading with deduplication and intelligent bundling
			(function() {
				// Prevent multiple initializations
				if (window.CheersMPScriptLoader) return;
				window.CheersMPScriptLoader = true;

				// Core bundle contains all essential functionality
				const coreBundle = '/scripts/core-bundle.js';

				// Page-specific bundles for enhanced functionality
				const pageBundles = {
					'/products': '/scripts/products-bundle.js',
					'/admin': '/scripts/admin-panel.js'
				};

				// High-performance script loader with deduplication
				function loadScript(src, priority = 'normal') {
					return new Promise((resolve, reject) => {
						// Check if script already loaded or loading
						const existing = document.querySelector(`script[src="${src}"]`);
						if (existing) {
							if (existing.dataset.loaded === 'true') {
								resolve();
							} else {
								existing.addEventListener('load', resolve);
								existing.addEventListener('error', reject);
							}
							return;
						}

						const script = document.createElement('script');
						script.src = src;
						script.async = true;

						// Set loading priority for modern browsers
						if (priority === 'high' && 'fetchpriority' in script) {
							script.fetchpriority = 'high';
						}

						script.onload = () => {
							script.dataset.loaded = 'true';
							console.log(`✓ Loaded: ${src}`);
							resolve();
						};

						script.onerror = () => {
							console.warn(`✗ Failed to load: ${src}`);
							reject(new Error(`Failed to load script: ${src}`));
						};

						document.head.appendChild(script);
					});
				}

				// Optimized script loading with better error handling
				async function loadOptimizedScripts() {
					try {
						// Load core bundle with high priority
						await loadScript(coreBundle, 'high');

						// Load page-specific bundle if needed (non-blocking)
						const currentPath = window.location.pathname;
						const pageBundle = Object.keys(pageBundles).find(path =>
							currentPath.includes(path)
						);

						if (pageBundle) {
							// Load page bundle asynchronously without blocking
							loadScript(pageBundles[pageBundle]).catch(err => {
								console.warn('Page-specific bundle failed to load:', err);
							});
						}

						// Load toast notifications if not already included
						if (!window.showToast) {
							loadScript('/scripts/toast-notifications.js').catch(err => {
								console.warn('Toast notifications failed to load:', err);
							});
						}

					} catch (error) {
						console.error('Core bundle loading failed:', error);
						// Only load essential fallbacks if core bundle completely fails
						loadCriticalFallbacks();
					}
				}

				// Minimal fallback loading only for critical failures
				function loadCriticalFallbacks() {
					console.warn('Loading critical fallbacks due to core bundle failure');

					const criticalScripts = [
						'/scripts/toast-notifications.js'
					];

					criticalScripts.forEach((src, index) => {
						setTimeout(() => {
							loadScript(src).catch(() => {
								console.error(`Critical fallback failed: ${src}`);
							});
						}, index * 50);
					});
				}

				// Smart initialization with optimal timing
				function smartInit() {
					if (document.readyState === 'loading') {
						document.addEventListener('DOMContentLoaded', loadOptimizedScripts);
					} else {
						// DOM already loaded, start immediately
						loadOptimizedScripts();
					}
				}

				// Initialize with performance monitoring
				const startTime = performance.now();
				smartInit();

				// Log loading performance
				window.addEventListener('load', () => {
					const loadTime = performance.now() - startTime;
					console.log(`📊 Script loading completed in ${Math.round(loadTime)}ms`);
				});
			})();
		</script>

		<!-- Snipcart Custom Styling -->
		<style is:global>
			/* Ensure Snipcart modal appears above everything including toasts */
			.snipcart-modal__container {
				z-index: 10001 !important;
			}

			.snipcart__modal {
				z-index: 10001 !important;
			}

			/* Match Cheers Marketplace branding */
			.snipcart-layout {
				font-family: system-ui, -apple-system, sans-serif !important;
			}

			.snipcart__box--badge-highlight {
				background-color: #2563eb !important;
			}

			.snipcart__button--primary {
				background-color: #2563eb !important;
				border-color: #2563eb !important;
			}

			.snipcart__button--primary:hover {
				background-color: #1d4ed8 !important;
				border-color: #1d4ed8 !important;
			}
		</style>
	</body>
</html>


