import type { APIRoute } from 'astro';

/**
 * Deployment webhook endpoint for Cloudflare Pages
 * This endpoint is called by Cloudflare Pages after successful deployments
 * to trigger cache purging and other post-deployment tasks
 */

interface CloudflareDeploymentPayload {
  id: string;
  short_id: string;
  project_id: string;
  project_name: string;
  environment: string;
  url: string;
  created_on: string;
  modified_on: string;
  latest_stage: {
    name: string;
    status: string;
    started_on: string;
    ended_on: string;
  };
  deployment_trigger: {
    type: string;
    metadata: {
      branch: string;
      commit_hash: string;
      commit_message: string;
    };
  };
  stages: Array<{
    name: string;
    status: string;
    started_on: string;
    ended_on: string;
  }>;
  build_config: {
    build_command: string;
    destination_dir: string;
    root_dir: string;
  };
  source: {
    type: string;
    config: {
      owner: string;
      repo_name: string;
      production_branch: string;
    };
  };
  env_vars: Record<string, any>;
  compatibility_date: string;
  compatibility_flags: string[];
  usage_model: string;
  aliases: string[];
  kv_namespaces: Record<string, any>;
  durable_object_namespaces: Record<string, any>;
  queue_producers: Record<string, any>;
  service_bindings: Record<string, any>;
  script_name: string;
  logpush: boolean;
  data: Record<string, any>;
}

interface PurgeOptions {
  urls?: string[];
  tags?: string[];
}

async function purgeCloudflareCache(zoneId: string, apiToken: string, options: PurgeOptions) {
  try {
    const purgeEndpoint = `https://api.cloudflare.com/client/v4/zones/${zoneId}/purge_cache`;
    
    const purgeData = options.urls && options.urls.length > 0
      ? { files: options.urls } // Purge specific URLs
      : options.tags && options.tags.length > 0
      ? { tags: options.tags } // Purge by tags
      : { purge_everything: true }; // Purge everything
    
    console.log('Purging Cloudflare cache:', purgeData);
    
    const response = await fetch(purgeEndpoint, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(purgeData)
    });

    const result = await response.json();
    
    if (response.ok && result.success) {
      console.log('Cache purged successfully:', result);
      return { success: true, result };
    } else {
      console.error('Cache purge failed:', result);
      return { success: false, error: result.errors || 'Unknown error' };
    }
  } catch (error) {
    console.error('Cache purge error:', error);
    return { success: false, error: error instanceof Error ? error.message : String(error) };
  }
}

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    console.log('=== DEPLOYMENT WEBHOOK RECEIVED ===');
    
    // Parse the deployment payload
    const payload: CloudflareDeploymentPayload = await request.json();
    
    console.log('Deployment info:', {
      id: payload.id,
      environment: payload.environment,
      status: payload.latest_stage?.status,
      project: payload.project_name,
      url: payload.url
    });

    // Only process successful production deployments
    if (payload.environment !== 'production' || payload.latest_stage?.status !== 'success') {
      console.log('Skipping cache purge - not a successful production deployment');
      return new Response(JSON.stringify({
        success: true,
        message: 'Webhook received but no action taken',
        reason: `Environment: ${payload.environment}, Status: ${payload.latest_stage?.status}`
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get Cloudflare credentials from environment
    const env = (locals as any)?.runtime?.env;
    const zoneId = env?.CLOUDFLARE_ZONE_ID;
    const apiToken = env?.CLOUDFLARE_API_TOKEN;

    if (!zoneId || !apiToken) {
      console.warn('Cloudflare cache purging not configured - missing ZONE_ID or API_TOKEN');
      return new Response(JSON.stringify({
        success: true,
        message: 'Deployment successful but cache purging not configured',
        deployment_id: payload.id
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    console.log('=== PURGING CLOUDFLARE CACHE ===');
    
    // Purge by tags for a full site update, which is more effective
    const tagsToPurge = [
      'html', 'api', 'seo', 'static'
    ];

    // Purge cache
    const purgeResult = await purgeCloudflareCache(zoneId, apiToken, { tags: tagsToPurge });

    if (purgeResult.success) {
      console.log('✅ Cache purged successfully after deployment');
      
      return new Response(JSON.stringify({
        success: true,
        message: 'Deployment successful and cache purged',
        deployment_id: payload.id,
        deployment_url: payload.url,
        cache_purge: {
          success: true,
          tags_purged: tagsToPurge,
          purge_id: purgeResult.result?.id
        }
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    } else {
      console.error('❌ Cache purge failed after deployment');
      
      return new Response(JSON.stringify({
        success: false,
        message: 'Deployment successful but cache purge failed',
        deployment_id: payload.id,
        deployment_url: payload.url,
        cache_purge: {
          success: false,
          error: purgeResult.error
        }
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

  } catch (error) {
    console.error('Deployment webhook error:', error);

    return new Response(JSON.stringify({
      success: false,
      message: 'Webhook processing failed',
      error: error instanceof Error ? error.message : String(error)
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

// Handle GET requests for testing
export const GET: APIRoute = async ({ locals }) => {
  const env = (locals as any)?.runtime?.env;
  
  return new Response(JSON.stringify({
    message: 'Deployment webhook endpoint is active',
    timestamp: new Date().toISOString(),
    cache_purging_configured: !!(env?.CLOUDFLARE_ZONE_ID && env?.CLOUDFLARE_API_TOKEN),
    zone_id_configured: !!env?.CLOUDFLARE_ZONE_ID,
    api_token_configured: !!env?.CLOUDFLARE_API_TOKEN
  }), {
    status: 200,
    headers: { 'Content-Type': 'application/json' }
  });
};
