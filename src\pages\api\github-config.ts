import type { APIRoute } from 'astro';
import { createGitHubService } from '../../lib/github';

export const GET: APIRoute = async ({ locals }) => {
  try {
    console.log('GitHub config check - environment available:', (locals as any)?.runtime?.env ? Object.keys((locals as any).runtime.env) : 'no env');
    const githubService = createGitHubService((locals as any)?.runtime?.env);
    
    if (!githubService) {
      return new Response(JSON.stringify({
        configured: false,
        message: 'GitHub integration not configured',
        required: ['GITHUB_OWNER', 'GITHUB_REPO', 'GITHUB_TOKEN'],
        optional: ['GITHUB_BRANCH (defaults to main)']
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Test the connection
    const testResult = await githubService.testConnection();
    
    return new Response(JSON.stringify({
      configured: true,
      connected: testResult.success,
      repository: testResult.repoInfo,
      error: testResult.error,
      timestamp: new Date().toISOString()
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('GitHub config check error:', error);
    
    return new Response(JSON.stringify({
      configured: false,
      error: error.message,
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    const body = await request.json();

    if (body.action === 'test-commit') {
      const githubService = createGitHubService((locals as any)?.runtime?.env);
      
      if (!githubService) {
        return new Response(JSON.stringify({
          success: false,
          error: 'GitHub service not configured'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // Test commit with a simple test file
      const testContent = JSON.stringify({
        test: true,
        timestamp: new Date().toISOString(),
        message: 'GitHub integration test from admin panel'
      }, null, 2);

      const result = await githubService.commitFile({
        message: 'Test commit from Cheers Marketplace admin panel',
        filePath: '.github/admin-test.json',
        content: testContent
      });

      return new Response(JSON.stringify(result), {
        status: result.success ? 200 : 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    return new Response(JSON.stringify({
      error: 'Invalid action. Supported actions: test-commit'
    }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('GitHub config action error:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
