import type { APIRoute } from 'astro';
import { getCacheConfig, purgeCloudflareCache, purgeCacheByTags } from '../../utils/cache';

/**
 * Manual cache purging endpoint for testing and emergency use
 * This endpoint allows manual cache purging for testing purposes
 */

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    console.log('=== MANUAL CACHE PURGE REQUESTED ===');
    
    const env = (locals as any)?.runtime?.env;
    const cacheConfig = getCacheConfig(env);
    
    if (!cacheConfig.configured) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Cache purging not configured',
        missing_vars: cacheConfig.missing,
        help: 'Set CLOUDFLARE_ZONE_ID and CLOUDFLARE_API_TOKEN environment variables'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Parse request body for options
    let options = {};
    try {
      options = await request.json();
    } catch {
      // Use defaults if no body provided
    }

    const {
      urls,
      tags,
      purgeEverything = false,
      purgeProducts = true
    } = options as any;

    console.log('Cache purge options:', { urls, tags, purgeEverything, purgeProducts });

    let result;

    if (purgeEverything) {
      // Purge everything
      console.log('Purging entire cache...');
      result = await purgeCloudflareCache({
        zoneId: env.CLOUDFLARE_ZONE_ID,
        apiToken: env.CLOUDFLARE_API_TOKEN,
        purgeEverything: true
      });
    } else if (urls && urls.length > 0) {
      // Purge specific URLs
      console.log('Purging specific URLs:', urls);
      result = await purgeCloudflareCache({
        zoneId: env.CLOUDFLARE_ZONE_ID,
        apiToken: env.CLOUDFLARE_API_TOKEN,
        urls
      });
    } else if (tags && tags.length > 0) {
      // Purge by tags
      console.log('Purging by tags:', tags);
      result = await purgeCloudflareCache({
        zoneId: env.CLOUDFLARE_ZONE_ID,
        apiToken: env.CLOUDFLARE_API_TOKEN,
        tags
      });
    } else if (purgeProducts) {
      // Default: purge product-related cache
      const tagsToPurge = ['products', 'html', 'api', 'seo'];
      console.log(`Purging product-related cache by tags: ${tagsToPurge.join(', ')}`);
      result = await purgeCacheByTags(
        env.CLOUDFLARE_ZONE_ID,
        env.CLOUDFLARE_API_TOKEN,
        tagsToPurge
      );
    } else {
      return new Response(JSON.stringify({
        success: false,
        error: 'No purge target specified',
        help: 'Specify urls, tags, purgeEverything: true, or purgeProducts: true'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    if (result.success) {
      console.log('✅ Manual cache purge successful');
      return new Response(JSON.stringify({
        success: true,
        message: 'Cache purged successfully',
        purge_id: result.purgeId,
        urls_purged: result.urls?.length || 0,
        timestamp: result.timestamp
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    } else {
      console.error('❌ Manual cache purge failed:', result.error);
      return new Response(JSON.stringify({
        success: false,
        error: result.error,
        timestamp: result.timestamp
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

  } catch (error) {
    console.error('Manual cache purge error:', error);
    return new Response(JSON.stringify({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

// Handle GET requests to show cache purging status
export const GET: APIRoute = async ({ locals }) => {
  const env = (locals as any)?.runtime?.env;
  const cacheConfig = getCacheConfig(env);
  
  return new Response(JSON.stringify({
    message: 'Manual cache purging endpoint',
    timestamp: new Date().toISOString(),
    configuration: cacheConfig,
    usage: {
      purge_everything: 'POST with {"purgeEverything": true}',
      purge_products: 'POST with {"purgeProducts": true} (default)',
      purge_urls: 'POST with {"urls": ["https://example.com/page1", "https://example.com/page2"]}',
      purge_tags: 'POST with {"tags": ["products", "api"]}'
    },
    examples: {
      curl_purge_products: 'curl -X POST /api/purge-cache -H "Content-Type: application/json" -d \'{"purgeProducts": true}\'',
      curl_purge_everything: 'curl -X POST /api/purge-cache -H "Content-Type: application/json" -d \'{"purgeEverything": true}\'',
      curl_purge_specific: 'curl -X POST /api/purge-cache -H "Content-Type: application/json" -d \'{"urls": ["/", "/products"]}\''
    }
  }), {
    status: 200,
    headers: { 'Content-Type': 'application/json' }
  });
};
