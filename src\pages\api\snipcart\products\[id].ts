import type { APIRoute } from 'astro';
import { getAllProducts } from '../../../../lib/products';
import { convertToSnipcartProduct, validateSnipcartProduct, logProductActivity } from '../../../../utils/snipcart';

// Disable prerendering for this API route since it's dynamic
export const prerender = false;

// Snipcart product validation endpoint
// This endpoint is called by <PERSON><PERSON><PERSON><PERSON><PERSON> to validate products and fetch current prices
// URL format: /api/snipcart/products/{product-id}

export const GET: APIRoute = async ({ params }) => {
  try {
    const productId = params.id;
    
    if (!productId) {
      return new Response(JSON.stringify({ 
        error: 'Product ID is required' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get all products
    const products = await getAllProducts();
    
    // Find the product by ID
    const product = products.find(p => p.id === productId);
    
    if (!product) {
      logProductActivity('VALIDATION_FAILED', productId, 'Product not found');
      return new Response(JSON.stringify({
        error: 'Product not found',
        id: productId
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Validate product for Snipcart compatibility
    const validation = validateSnipcartProduct(product);
    if (!validation.valid) {
      logProductActivity('VALIDATION_FAILED', productId, validation.errors);
      return new Response(JSON.stringify({
        error: 'Product validation failed',
        id: productId,
        validationErrors: validation.errors
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Convert to Snipcart format
    const snipcartProduct = convertToSnipcartProduct(product);

    logProductActivity('VALIDATION_SUCCESS', productId, { name: product.name, price: product.price });

    return new Response(JSON.stringify(snipcartProduct), {
      status: 200,
      headers: { 
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=300' // Cache for 5 minutes
      }
    });

  } catch (error) {
    console.error('Error validating product:', error);
    
    return new Response(JSON.stringify({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
