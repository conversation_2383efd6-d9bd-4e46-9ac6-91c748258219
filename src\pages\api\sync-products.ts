import type { APIRoute } from 'astro';
import { createGitHubService } from '../../lib/github';
import { generateSlug } from '../../lib/products';
import { purgeCacheByTags, getCacheConfig } from '../../utils/cache';

// Import products data directly since we can't write to filesystem in Cloudflare Pages
import productsData from '../../data/products.json';

// Notify <PERSON><PERSON><PERSON><PERSON><PERSON> about product updates (for cache invalidation)
async function notifySnipcartProductUpdate(productId: string) {
  try {
    // Snipcart automatically validates products when they're added to cart
    // But we can log this for debugging
    console.log('Product updated, <PERSON><PERSON><PERSON><PERSON><PERSON> will validate on next cart add:', productId);

    // If you need to proactively update Snipcart's cache, you could call their API here
    // But for most use cases, their automatic validation is sufficient

    return true;
  } catch (error) {
    console.error('Error notifying <PERSON><PERSON><PERSON><PERSON><PERSON> about product update:', error);
    return false;
  }
}

export const GET: APIRoute = async () => {
  try {
    console.log('Sync products GET called');

    // Use imported products data since we can't read files in Cloudflare Pages
    const products = productsData || [];

    console.log(`Found ${products.length} products`);

    return new Response(JSON.stringify({
      success: true,
      products,
      count: products.length,
      lastModified: new Date().toISOString()
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Failed to read products:', error);

    return new Response(JSON.stringify({
      success: false,
      error: `Failed to read products: ${error instanceof Error ? error.message : String(error)}`,
      stack: error instanceof Error ? error.stack : undefined
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

export const POST: APIRoute = async ({ request, locals }) => {
  console.log('=== SYNC PRODUCTS API CALLED ===');
  console.log('Timestamp:', new Date().toISOString());
  console.log('Environment variables available:', (locals as any)?.runtime?.env ? Object.keys((locals as any).runtime.env) : 'no env');

  try {
    console.log('=== PARSING REQUEST BODY ===');
    // Parse request body
    let body;
    try {
      body = await request.json();
      console.log('Request body successfully parsed');
      console.log('Body type:', typeof body);
      console.log('Body keys:', Object.keys(body || {}));
    } catch (parseError) {
      console.error('Failed to parse request body:', parseError);
      return new Response(JSON.stringify({
        success: false,
        error: 'Invalid JSON in request body'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    console.log('=== VALIDATING REQUEST DATA ===');
    console.log('Request body parsed, products count:', body.products?.length);
    console.log('Products type:', typeof body.products);
    console.log('Is products array:', Array.isArray(body.products));
    if (body.products && Array.isArray(body.products)) {
      console.log('Product IDs:', body.products.map((p: any) => p.id));
    }

    if (!body.products || !Array.isArray(body.products)) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Invalid products data - expected array'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Validate each product
    const validatedProducts = body.products.map((product: any, index: number) => {
      const errors = [];
      
      if (!product.name || typeof product.name !== 'string') {
        errors.push(`Product ${index}: name is required`);
      }
      
      if (!product.category || typeof product.category !== 'string') {
        errors.push(`Product ${index}: category is required`);
      }
      
      if (!product.description || typeof product.description !== 'string') {
        errors.push(`Product ${index}: description is required`);
      }
      
      if (typeof product.price !== 'number' || product.price <= 0) {
        errors.push(`Product ${index}: price must be a positive number`);
      }
      
      if (errors.length > 0) {
        throw new Error(errors.join(', '));
      }
      
      // Ensure required fields and generate slug if missing
      return {
        id: product.id || Date.now().toString() + Math.random().toString(36).substring(2, 11),
        name: product.name.trim(),
        category: product.category.trim(),
        description: product.description.trim(),
        price: Number(product.price),
        images: Array.isArray(product.images) ? product.images.filter((img: any) => img && img.trim()) : [],
        keyPoints: Array.isArray(product.keyPoints) ? product.keyPoints : [],
        defects: product.defects || null,
        slug: product.slug || generateSlug(product.name),
        createdAt: product.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
    });
    
    // Note: In Cloudflare Pages, we can't write to the filesystem
    // The products will only be persisted via GitHub commit
    console.log(`Validated ${validatedProducts.length} products for sync`);

    console.log('=== GITHUB COMMIT PROCESS ===');
    // Commit to GitHub (this will trigger Cloudflare Pages build automatically)
    const githubService = createGitHubService((locals as any)?.runtime?.env);
    console.log('GitHub service created:', githubService ? 'SUCCESS' : 'FAILED');
    let githubCommitResult = null;
    let cacheResult = null;

    if (githubService) {
      try {
        console.log('=== COMMITTING TO GITHUB ===');
        console.log('Committing products to GitHub...');
        console.log('Number of products to commit:', validatedProducts.length);
        console.log('Product IDs to commit:', validatedProducts.map((p: any) => p.id));

        githubCommitResult = await githubService.commitProducts(validatedProducts);
        console.log('GitHub commit result:', JSON.stringify(githubCommitResult, null, 2));

        if (githubCommitResult.success) {
          console.log(`✅ Successfully committed products to GitHub. Commit SHA: ${githubCommitResult.commitSha}`);

          // Trigger cache purge after successful GitHub commit
          console.log('=== TRIGGERING CACHE PURGE ===');
          const env = (locals as any)?.runtime?.env;
          const cacheConfig = getCacheConfig(env);

          if (cacheConfig.configured) {
            try {
              // Purge all product-related content using tags.
              // This is more robust and covers all affected pages (homepage, listings, product pages, api).
              const tagsToPurge = ['products', 'html', 'api', 'seo'];
              console.log(`Purging cache for tags: ${tagsToPurge.join(', ')}`);

              cacheResult = await purgeCacheByTags(
                env.CLOUDFLARE_ZONE_ID,
                env.CLOUDFLARE_API_TOKEN,
                tagsToPurge
              );

              if (cacheResult.success) {
                console.log('✅ Cache purged successfully after GitHub commit');
              } else {
                console.warn('⚠️ Cache purge failed after GitHub commit:', cacheResult.error);
              }
            } catch (cacheError) {
              console.warn('⚠️ Cache purge error:', cacheError);
              cacheResult = { success: false, error: cacheError instanceof Error ? cacheError.message : String(cacheError), timestamp: new Date().toISOString() };
            }
          } else {
            console.log('ℹ️ Cache purging not configured, skipping');
            cacheResult = { success: false, error: 'Cache purging not configured', timestamp: new Date().toISOString() };
          }
        } else {
          console.error('❌ Failed to commit to GitHub:', githubCommitResult.error);
        }
      } catch (githubError) {
        console.error('❌ GitHub commit error (exception):', githubError);
        githubCommitResult = { success: false, error: githubError instanceof Error ? githubError.message : String(githubError) };
      }
    } else {
      console.warn('⚠️ GitHub service not configured. Skipping GitHub commit.');
    }

    // Check if GitHub service was configured and if commit was expected to work
    const githubConfigured = githubService !== null;
    let fallbackUsed = false;

    // If GitHub is configured but commit failed, this is an error
    if (githubConfigured && !githubCommitResult?.success) {
      console.error('GitHub is configured but commit failed:', githubCommitResult?.error);

      // Try fallback build hook as last resort
      try {
        const buildHookUrl = (locals as any)?.runtime?.env?.CLOUDFLARE_BUILD_HOOK_URL;
        if (buildHookUrl) {
          console.log('GitHub commit failed, trying direct build hook...');
          const hookResponse = await fetch(buildHookUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              source: 'product-sync-fallback',
              trigger: 'products_updated',
              timestamp: new Date().toISOString()
            })
          });

          if (hookResponse.ok) {
            console.log('Direct build hook triggered successfully after GitHub failure');
            fallbackUsed = true;
          } else {
            console.error('Direct build hook also failed:', hookResponse.status, hookResponse.statusText);
          }
        }
      } catch (buildError) {
        console.error('Failed to trigger fallback build hook:', buildError);
      }

      // If GitHub was configured but failed, and no fallback worked, return error
      if (!fallbackUsed) {
        return new Response(JSON.stringify({
          success: false,
          error: `GitHub commit failed: ${githubCommitResult?.error || 'Unknown error'}`,
          count: validatedProducts.length,
          github: {
            configured: true,
            committed: false,
            error: githubCommitResult?.error
          }
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }

    // Notify Snipcart about product updates
    if (validatedProducts.length > 0) {
      for (const product of validatedProducts) {
        await notifySnipcartProductUpdate(product.id);
      }
    }

    // Determine success status and message
    const overallSuccess = githubCommitResult?.success || fallbackUsed || !githubConfigured;
    let message = `Successfully processed ${validatedProducts.length} products`;

    if (githubCommitResult?.success) {
      message += ' and committed to GitHub';
    } else if (fallbackUsed) {
      message += ' using fallback deployment trigger (GitHub commit failed)';
    } else if (!githubConfigured) {
      message += ' (GitHub not configured - manual deployment needed)';
    }

    const responseData = {
      success: overallSuccess,
      message,
      count: validatedProducts.length,
      environment: 'cloudflare-pages',
      github: githubCommitResult ? {
        committed: githubCommitResult.success,
        commitSha: githubCommitResult.commitSha,
        error: githubCommitResult.error
      } : { configured: githubConfigured },
      cache: cacheResult ? {
        purged: cacheResult.success,
        error: cacheResult.error,
        timestamp: cacheResult.timestamp
      } : { configured: false, purged: false },
      fallbackUsed,
      note: 'Products are persisted via GitHub commit only (no local file system in Cloudflare Pages)'
    };

    console.log('Sending response:', responseData);

    return new Response(JSON.stringify(responseData), {
      status: overallSuccess ? 200 : 500,
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('Product sync error:', error);
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack available');

    const errorResponse = {
      success: false,
      error: error instanceof Error ? error.message : String(error) || 'Failed to sync products',
      details: error instanceof Error && error.stack ? error.stack.split('\n').slice(0, 3).join('\n') : 'No stack trace'
    };

    console.log('Sending error response:', errorResponse);

    return new Response(JSON.stringify(errorResponse), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
