import type { APIRoute } from 'astro';

// Snipcart webhook handler
// This endpoint receives notifications from <PERSON><PERSON><PERSON><PERSON><PERSON> about order events
// Configure this URL in your Snipcart dashboard: https://app.snipcart.com/dashboard/webhooks

interface SnipcartWebhookEvent {
  eventName: string;
  mode: 'Test' | 'Live';
  createdOn: string;
  content: any;
  from?: string;
  to?: string;
  trackingNumber?: string;
  trackingUrl?: string;
  orderToken?: string;
  amount?: number;
  comment?: string;
  notifiedCustomerByEmail?: boolean;
  currency?: string;
  notificationType?: string;
  sentByEmail?: boolean;
  sentByEmailOn?: string;
  subject?: string;
  message?: string;
}

// Verify webhook authenticity using <PERSON><PERSON><PERSON><PERSON><PERSON>'s request token
async function verifyWebhookToken(token: string): Promise<boolean> {
  try {
    const response = await fetch(`https://app.snipcart.com/api/requestvalidation/${token}`);
    return response.ok;
  } catch (error) {
    console.error('Error verifying webhook token:', error);
    return false;
  }
}

// Log order for debugging and record keeping
function logOrder(event: SnipcartWebhookEvent) {
  console.log('=== SNIPCART ORDER EVENT ===');
  console.log('Event:', event.eventName);
  console.log('Mode:', event.mode);
  console.log('Created:', event.createdOn);
  
  if (event.content) {
    console.log('Order Details:');
    console.log('- Invoice:', event.content.invoiceNumber);
    console.log('- Email:', event.content.email);
    console.log('- Total:', event.content.total, event.content.currency?.toUpperCase());
    console.log('- Items:', event.content.itemsCount);
    console.log('- Status:', event.content.status);
    console.log('- Payment Status:', event.content.paymentStatus);
    
    if (event.content.items && event.content.items.length > 0) {
      console.log('- Products:');
      event.content.items.forEach((item: any, index: number) => {
        console.log(`  ${index + 1}. ${item.name} (${item.quantity}x $${item.price})`);
      });
    }
  }
  console.log('========================');
}

// Send order notification email (you can integrate with your email service)
async function sendOrderNotification(event: SnipcartWebhookEvent) {
  // TODO: Integrate with your email service (SendGrid, Mailgun, etc.)
  // For now, we'll just log it
  console.log('📧 Order notification would be sent for:', event.content?.invoiceNumber);
}

// Update inventory (if you track inventory)
async function updateInventory(event: SnipcartWebhookEvent) {
  // TODO: Update your inventory system
  // For used goods marketplace, you might want to mark items as sold
  if (event.content?.items) {
    console.log('📦 Inventory update needed for items:', 
      event.content.items.map((item: any) => item.id).join(', ')
    );
  }
}

export const POST: APIRoute = async ({ request }) => {
  try {
    // Verify the request is from Snipcart
    const requestToken = request.headers.get('X-Snipcart-RequestToken');
    
    if (!requestToken) {
      console.error('Missing Snipcart request token');
      return new Response(JSON.stringify({ error: 'Missing request token' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Verify token authenticity
    const isValidToken = await verifyWebhookToken(requestToken);
    if (!isValidToken) {
      console.error('Invalid Snipcart request token');
      return new Response(JSON.stringify({ error: 'Invalid request token' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Parse the webhook payload
    const event: SnipcartWebhookEvent = await request.json();
    
    // Log the event for debugging
    console.log('Received Snipcart webhook:', event.eventName, 'Mode:', event.mode);
    
    // Handle different event types
    switch (event.eventName) {
      case 'order.completed':
        logOrder(event);
        await sendOrderNotification(event);
        await updateInventory(event);
        break;
        
      case 'order.status.changed':
        console.log(`Order status changed from ${event.from} to ${event.to}`);
        logOrder(event);
        break;
        
      case 'order.paymentStatus.changed':
        console.log(`Payment status changed from ${event.from} to ${event.to}`);
        logOrder(event);
        break;
        
      case 'order.trackingNumber.changed':
        console.log(`Tracking number updated: ${event.trackingNumber}`);
        if (event.trackingUrl) {
          console.log(`Tracking URL: ${event.trackingUrl}`);
        }
        break;
        
      case 'order.refund.created':
        console.log(`Refund created: $${event.amount} ${event.currency}`);
        if (event.comment) {
          console.log(`Refund reason: ${event.comment}`);
        }
        break;
        
      case 'order.notification.created':
        console.log(`Notification created: ${event.notificationType}`);
        if (event.message) {
          console.log(`Message: ${event.message}`);
        }
        break;
        
      default:
        console.log('Unhandled webhook event:', event.eventName);
    }

    // Return success response (required by Snipcart)
    return new Response(JSON.stringify({ 
      success: true, 
      message: 'Webhook processed successfully',
      eventName: event.eventName 
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error processing Snipcart webhook:', error);
    
    return new Response(JSON.stringify({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

// Handle GET requests for webhook verification/testing
export const GET: APIRoute = async () => {
  return new Response(JSON.stringify({ 
    message: 'Snipcart webhook endpoint is active',
    timestamp: new Date().toISOString()
  }), {
    status: 200,
    headers: { 'Content-Type': 'application/json' }
  });
};
