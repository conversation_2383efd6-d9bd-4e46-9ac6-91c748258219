---
export const prerender = true;

// Debug page to test sync functionality
---

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Debug Sync - Cheers Marketplace</title>
  <style>
    body {
      font-family: system-ui, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 2rem;
      line-height: 1.6;
    }
    .test-section {
      margin: 2rem 0;
      padding: 1rem;
      border: 1px solid #ddd;
      border-radius: 8px;
    }
    button {
      background: #2563eb;
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 4px;
      cursor: pointer;
      margin: 0.5rem 0.5rem 0.5rem 0;
    }
    button:hover {
      background: #1d4ed8;
    }
    .result {
      background: #f8f9fa;
      padding: 1rem;
      border-radius: 4px;
      margin: 1rem 0;
      white-space: pre-wrap;
      font-family: monospace;
      font-size: 0.875rem;
    }
    .error {
      background: #fef2f2;
      border: 1px solid #fecaca;
      color: #dc2626;
    }
    .success {
      background: #f0fdf4;
      border: 1px solid #bbf7d0;
      color: #16a34a;
    }
  </style>
</head>
<body>
  <h1>🔧 Sync Debug Tool</h1>
  <p>This page helps debug sync issues with the admin panel.</p>

  <div class="test-section">
    <h2>1. Test GitHub Configuration</h2>
    <button onclick="testGitHub()">Test GitHub Config</button>
    <div id="github-result" class="result" style="display: none;"></div>
  </div>

  <div class="test-section">
    <h2>2. Test Products API</h2>
    <button onclick="testProducts()">Test Products API</button>
    <div id="products-result" class="result" style="display: none;"></div>
  </div>

  <div class="test-section">
    <h2>3. Test Sync Products (GET)</h2>
    <button onclick="testSyncGet()">Test Sync GET</button>
    <div id="sync-get-result" class="result" style="display: none;"></div>
  </div>

  <div class="test-section">
    <h2>4. Test Sync Products (POST)</h2>
    <button onclick="testSyncPost()">Test Sync POST</button>
    <div id="sync-post-result" class="result" style="display: none;"></div>
  </div>

  <div class="test-section">
    <h2>5. Test with Sample Product</h2>
    <button onclick="testWithSampleProduct()">Test Sample Product Sync</button>
    <div id="sample-result" class="result" style="display: none;"></div>
  </div>

  <script is:inline>
    window.debugTools = {
      async testEndpoint(url, options = {}) {
        try {
          console.log(`Testing: ${url}`, options);
          const response = await fetch(url, options);

          const responseInfo = {
            status: response.status,
            statusText: response.statusText,
            headers: Object.fromEntries(response.headers.entries()),
            ok: response.ok
          };

          let responseData;
          const contentType = response.headers.get('content-type');

          if (contentType && contentType.includes('application/json')) {
            try {
              responseData = await response.json();
            } catch (jsonError) {
              const text = await response.text();
              responseData = {
                error: 'Failed to parse JSON',
                jsonError: jsonError.message,
                rawText: text
              };
            }
          } else {
            responseData = await response.text();
          }

          return {
            success: response.ok,
            response: responseInfo,
            data: responseData
          };
        } catch (error) {
          return {
            success: false,
            error: error.message,
            stack: error.stack
          };
        }
      },

      displayResult(elementId, result) {
        const element = document.getElementById(elementId);
        element.style.display = 'block';
        element.className = `result ${result.success ? 'success' : 'error'}`;
        element.textContent = JSON.stringify(result, null, 2);
      }
    };

    async function testGitHub() {
      const result = await window.debugTools.testEndpoint('/api/github-config');
      window.debugTools.displayResult('github-result', result);
    }

    async function testProducts() {
      const result = await window.debugTools.testEndpoint('/api/products.json');
      window.debugTools.displayResult('products-result', result);
    }

    async function testSyncGet() {
      const result = await window.debugTools.testEndpoint('/api/sync-products');
      window.debugTools.displayResult('sync-get-result', result);
    }

    async function testSyncPost() {
      const result = await window.debugTools.testEndpoint('/api/sync-products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          products: []
        })
      });
      window.debugTools.displayResult('sync-post-result', result);
    }

    async function testWithSampleProduct() {
      const sampleProduct = {
        id: 'test-' + Date.now(),
        name: 'Test Product',
        category: 'Test Category',
        description: 'This is a test product for debugging sync issues.',
        price: 99.99,
        images: ['https://placehold.co/300x225/e2e8f0/64748b?text=Test+Product'],
        keyPoints: [
          { label: 'Test Feature', value: 'Test Value' }
        ],
        defects: null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      const result = await window.debugTools.testEndpoint('/api/sync-products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          products: [sampleProduct]
        })
      });
      window.debugTools.displayResult('sample-result', result);
    }
  </script>
</body>
</html>
