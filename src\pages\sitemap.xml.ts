import type { APIRoute } from 'astro';
import { generateSlug } from '../lib/products';
import products from '../data/products.json';

export const GET: APIRoute = async ({ site }) => {
  const baseUrl = site?.toString() || 'https://www.cheersmarketplace.com';
  
  // Static pages with SEO metadata
  const staticPages = [
    { url: '', priority: '1.0', changefreq: 'daily' },
    { url: '/products', priority: '0.9', changefreq: 'daily' },
    { url: '/about', priority: '0.7', changefreq: 'monthly' },
    { url: '/faq', priority: '0.6', changefreq: 'monthly' },
    { url: '/privacy', priority: '0.5', changefreq: 'yearly' },
    { url: '/cart', priority: '0.4', changefreq: 'never' },
  ];
  
  // Dynamic product pages with metadata
  const productPages = products.map((product: any) => ({
    url: `/products/${generateSlug(product.name)}`,
    priority: '0.8',
    changefreq: 'weekly',
    lastmod: new Date().toISOString().split('T')[0]
  }));

  // Generate category pages
  const categories = [...new Set(products.map((p: any) => p.category))];
  const categoryPages = categories.map((category: string) => ({
    url: `/products?category=${encodeURIComponent(category)}`,
    priority: '0.7',
    changefreq: 'weekly'
  }));

  // Combine all pages
  const allPages = [...staticPages, ...productPages, ...categoryPages];
  
  // Generate sitemap XML
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${allPages.map(page => `  <url>
    <loc>${baseUrl}${page.url}</loc>
    <priority>${page.priority}</priority>
    <changefreq>${page.changefreq}</changefreq>
    ${page.lastmod ? `<lastmod>${page.lastmod}</lastmod>` : ''}
  </url>`).join('\n')}
</urlset>`;

  return new Response(sitemap, {
    status: 200,
    headers: {
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
    },
  });
};
