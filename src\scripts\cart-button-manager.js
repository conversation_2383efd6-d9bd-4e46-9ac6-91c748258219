/**
 * <PERSON><PERSON>
 * Handles add to cart button functionality and quantity selection
 */

window.CartButtonManager = {
  buttons: new Map(),

  init() {
    // Initialize all cart button containers
    document.querySelectorAll('.add-to-cart-container').forEach(container => {
      this.initializeButton(container);
    });

    // Listen for dynamically added buttons
    this.observeNewButtons();
  },

  initializeButton(container) {
    const productId = container.dataset.productId;
    if (!productId || this.buttons.has(productId)) return;

    const buttonInstance = new AddToCartButtonInstance(container);
    this.buttons.set(productId, buttonInstance);
  },

  observeNewButtons() {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === 1) {
            // Check if the node itself is a cart container
            if (node.classList?.contains('add-to-cart-container')) {
              this.initializeButton(node);
            }
            // Check for cart containers within the node
            node.querySelectorAll?.('.add-to-cart-container').forEach(container => {
              this.initializeButton(container);
            });
          }
        });
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  },

  getButton(productId) {
    return this.buttons.get(productId);
  }
};

class AddToCartButtonInstance {
  constructor(container) {
    this.container = container;
    this.button = container.querySelector('.cart-btn, .add-to-cart-btn');
    this.quantitySelector = container.querySelector('.quantity-selector');
    this.quantityDisplay = container.querySelector('.quantity-display');
    this.decreaseBtn = container.querySelector('.qty-decrease');
    this.increaseBtn = container.querySelector('.qty-increase');
    
    if (!this.button) return;

    this.productData = this.extractProductData();
    this.currentQuantity = 1;
    this.state = 'available'; // available, loading, added
    
    this.init();
  }

  extractProductData() {
    const dataset = this.button.dataset;
    return {
      id: dataset.itemId || dataset.productId,
      name: dataset.itemName || dataset.productName,
      price: parseFloat(dataset.itemPrice || dataset.productPrice),
      image: dataset.itemImage || dataset.productImage,
      slug: dataset.itemUrl || dataset.productSlug,
      category: dataset.itemCategories || dataset.productCategory,
      description: dataset.itemDescription || dataset.productDescription,
      keyPoints: this.parseKeyPoints(dataset.itemCustom1Value || dataset.productKeyPoints),
      maxQuantity: parseInt(dataset.itemMaxQuantity || dataset.maxQuantity) || 1
    };
  }

  parseKeyPoints(keyPointsStr) {
    try {
      return JSON.parse(keyPointsStr || '[]');
    } catch {
      return [];
    }
  }

  init() {
    // Bind button click
    this.button.addEventListener('click', (e) => {
      e.preventDefault();
      this.handleAddToCart();
    });

    // Bind quantity controls if they exist
    if (this.decreaseBtn) {
      this.decreaseBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        this.decreaseQuantity();
      });
    }

    if (this.increaseBtn) {
      this.increaseBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        this.increaseQuantity();
      });
    }

    // Initialize quantity display
    this.updateQuantityDisplay();
    this.updateButtonState();
  }

  handleAddToCart() {
    if (this.state === 'loading' || this.state === 'added') return;

    // Check if already in cart
    if (window.CartStateManager && window.CartStateManager.isInCart(this.productData.id)) {
      if (window.showCartError) {
        window.showCartError(`${this.productData.name} is already in your cart.`);
      }
      return;
    }

    this.setState('loading');

    // Show quantity selector if hidden
    if (this.quantitySelector && this.quantitySelector.style.display === 'none') {
      this.quantitySelector.style.display = 'flex';
    }

    // Simulate add to cart process (Snipcart will handle the actual addition)
    setTimeout(() => {
      this.setState('added');
      
      // Show success message
      if (window.showCartSuccess) {
        window.showCartSuccess(this.productData.name);
      }

      // Animate cart icon
      this.animateCartIcon();
    }, 800);
  }

  decreaseQuantity() {
    if (this.currentQuantity > 1) {
      this.currentQuantity--;
      this.updateQuantityDisplay();
      this.updateQuantityButtons();
    }
  }

  increaseQuantity() {
    if (this.currentQuantity < this.productData.maxQuantity) {
      this.currentQuantity++;
      this.updateQuantityDisplay();
      this.updateQuantityButtons();
    }
  }

  updateQuantityDisplay() {
    if (this.quantityDisplay) {
      this.quantityDisplay.textContent = this.currentQuantity;
      this.quantityDisplay.dataset.quantity = this.currentQuantity;
    }
  }

  updateQuantityButtons() {
    if (this.decreaseBtn) {
      this.decreaseBtn.disabled = this.currentQuantity <= 1;
    }
    if (this.increaseBtn) {
      this.increaseBtn.disabled = this.currentQuantity >= this.productData.maxQuantity;
    }
  }

  setState(newState) {
    this.state = newState;
    this.updateButtonState();
  }

  updateButtonState() {
    const textDefault = this.button.querySelector('.btn-text');
    const textLoading = this.button.querySelector('.btn-text-loading');
    const textAdded = this.button.querySelector('.btn-text-added');

    // Hide all text states
    [textDefault, textLoading, textAdded].forEach(el => {
      if (el) el.style.display = 'none';
    });

    // Remove all state classes
    this.button.classList.remove('loading', 'added');

    switch (this.state) {
      case 'loading':
        this.button.classList.add('loading');
        this.button.disabled = true;
        if (textLoading) textLoading.style.display = 'flex';
        break;

      case 'added':
        this.button.classList.add('added');
        this.button.disabled = true;
        if (textAdded) textAdded.style.display = 'flex';
        break;

      case 'available':
      default:
        this.button.disabled = false;
        if (textDefault) textDefault.style.display = 'flex';
        break;
    }
  }

  animateCartIcon() {
    const cartIcon = document.querySelector('.cart-trigger, .snipcart-checkout');
    if (cartIcon) {
      cartIcon.classList.add('cart-bounce');
      setTimeout(() => {
        cartIcon.classList.remove('cart-bounce');
      }, 600);
    }
  }

  reset() {
    this.currentQuantity = 1;
    this.setState('available');
    this.updateQuantityDisplay();
    this.updateQuantityButtons();
    
    if (this.quantitySelector) {
      this.quantitySelector.style.display = 'none';
    }
  }

  getQuantity() {
    return this.currentQuantity;
  }

  getProductData() {
    return this.productData;
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.CartButtonManager.init();
  });
} else {
  window.CartButtonManager.init();
}
