/**
 * Cart State Manager
 * Handles cart state management for Snipcart integration
 * Provides visual feedback and button state updates
 */

window.CartStateManager = {
  cartItems: new Set(),
  
  // Initialize cart state management
  init() {
    console.log('Initializing CartStateManager...');

    // Check if <PERSON>ni<PERSON><PERSON><PERSON> is already ready
    if (window.Snipcart && window.Snipcart.api) {
      console.log('Snipcart already ready');
      this.setupEventListeners();
      this.loadInitialCartState();
    } else {
      console.log('Waiting for Snipcar<PERSON> to be ready...');

      // Wait for Snipcart to be ready using multiple methods
      if (window.Snipcart) {
        window.Snipcart.events.on('snipcart.ready', () => {
          console.log('Snipcart ready event fired');
          this.setupEventListeners();
          this.loadInitialCartState();
        });
      } else {
        // Fallback: poll for Snipcart availability
        const checkSnipcart = () => {
          if (window.Snipcart && window.Snipcart.api) {
            console.log('Snipcart detected via polling');
            this.setupEventListeners();
            this.loadInitialCartState();
          } else {
            setTimeout(checkSnipcart, 500);
          }
        };
        checkSnipcart();
      }
    }
  },

  // Set up Snipcart event listeners
  setupEventListeners() {
    console.log('Setting up Snipcart event listeners...');

    // Use Snipcart's API to listen for events
    if (window.Snipcart && window.Snipcart.events) {
      // Item added to cart
      window.Snipcart.events.on('item.added', (item) => {
        console.log('Snipcart item.added event:', item);
        this.handleItemAdded(item);
      });

      // Item removed from cart
      window.Snipcart.events.on('item.removed', (item) => {
        console.log('Snipcart item.removed event:', item);
        this.handleItemRemoved(item);
      });

      // Item quantity updated
      window.Snipcart.events.on('item.updated', (item) => {
        console.log('Snipcart item.updated event:', item);
        this.handleItemUpdated(item);
      });

      // Cart cleared
      window.Snipcart.events.on('cart.cleared', () => {
        console.log('Snipcart cart.cleared event');
        this.handleCartCleared();
      });
    } else {
      console.warn('Snipcart not ready, will retry...');
      // Retry after a delay
      setTimeout(() => this.setupEventListeners(), 1000);
    }

    // Listen for add to cart button clicks for immediate feedback
    document.addEventListener('click', (event) => {
      if (event.target.classList.contains('snipcart-add-item') ||
          event.target.closest('.snipcart-add-item')) {
        const button = event.target.classList.contains('snipcart-add-item')
          ? event.target
          : event.target.closest('.snipcart-add-item');
        this.handleAddToCartClick(button);
      }
    });
  },

  // Load initial cart state
  loadInitialCartState() {
    console.log('Loading initial cart state...');

    try {
      if (window.Snipcart && window.Snipcart.api) {
        const cartItems = window.Snipcart.api.items.all();
        console.log('Current cart items:', cartItems);

        cartItems.forEach(item => {
          this.cartItems.add(item.id);
          console.log('Added item to tracking:', item.id, item.name);
        });

        this.updateAllButtonStates();
      }
    } catch (error) {
      console.error('Error loading initial cart state:', error);
    }
  },

  // Handle item added to cart
  handleItemAdded(item) {
    console.log('Item added to cart:', item);
    
    // Add to our tracking
    this.cartItems.add(item.id);
    
    // Show success toast
    if (window.showCartSuccess) {
      window.showCartSuccess(item.name);
    }
    
    // Update button states
    this.updateButtonState(item.id, 'added');
    
    // Animate cart icon
    this.animateCartIcon();
  },

  // Handle item removed from cart
  handleItemRemoved(item) {
    console.log('Item removed from cart:', item);
    
    // Remove from our tracking
    this.cartItems.delete(item.id);
    
    // Show removal toast
    if (window.showCartRemoved) {
      window.showCartRemoved(item.name);
    }
    
    // Update button states
    this.updateButtonState(item.id, 'available');
  },

  // Handle item quantity updated
  handleItemUpdated(item) {
    if (item.quantity === 0) {
      this.handleItemRemoved(item);
    } else {
      this.handleItemAdded(item);
    }
  },

  // Handle cart cleared
  handleCartCleared() {
    console.log('Cart cleared');
    
    // Clear our tracking
    this.cartItems.clear();
    
    // Update all button states
    this.updateAllButtonStates();
  },

  // Handle add to cart button click for immediate feedback
  handleAddToCartClick(button) {
    const productId = button.getAttribute('data-item-id');
    const productName = button.getAttribute('data-item-name');
    
    // Check if item is already in cart
    if (this.cartItems.has(productId)) {
      if (window.showCartError) {
        window.showCartError(`${productName} is already in your cart.`);
      }
      return;
    }
    
    // Show loading state immediately
    this.updateButtonState(productId, 'loading');
    
    // Reset to available state after a delay if add fails
    setTimeout(() => {
      if (!this.cartItems.has(productId)) {
        this.updateButtonState(productId, 'available');
      }
    }, 3000);
  },

  // Update button state for a specific product
  updateButtonState(productId, state) {
    const buttons = document.querySelectorAll(`[data-item-id="${productId}"]`);
    console.log(`Updating ${buttons.length} buttons for product ${productId} to state: ${state}`);

    buttons.forEach(button => {
      button.classList.remove('btn-loading', 'btn-added', 'btn-available');

      // Determine icon size based on button size
      const isLargeButton = button.classList.contains('btn-large');
      const iconSize = isLargeButton ? '20' : '16';

      switch (state) {
        case 'loading':
          button.classList.add('btn-loading');
          button.disabled = true;
          button.innerHTML = `
            <svg width="${iconSize}" height="${iconSize}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="animate-spin">
              <path d="M21 12a9 9 0 11-6.219-8.56"/>
            </svg>
            Adding...
          `;
          break;

        case 'added':
          button.classList.add('btn-added');
          button.disabled = true;
          button.innerHTML = `
            <svg width="${iconSize}" height="${iconSize}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M9 12l2 2 4-4"></path>
              <circle cx="12" cy="12" r="10"></circle>
            </svg>
            In Cart
          `;
          break;

        case 'available':
        default:
          button.classList.add('btn-available');
          button.disabled = false;
          const price = button.getAttribute('data-item-price');
          const priceText = price ? ` - $${parseFloat(price).toFixed(2)}` : '';
          button.innerHTML = `
            <svg width="${iconSize}" height="${iconSize}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="9" cy="21" r="1"></circle>
              <circle cx="20" cy="21" r="1"></circle>
              <path d="m1 1 4 4 2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
            </svg>
            Add to Cart${priceText}
          `;
          break;
      }
    });
  },

  // Update all button states based on current cart
  updateAllButtonStates() {
    const allButtons = document.querySelectorAll('.snipcart-add-item');
    
    allButtons.forEach(button => {
      const productId = button.getAttribute('data-item-id');
      const state = this.cartItems.has(productId) ? 'added' : 'available';
      this.updateButtonState(productId, state);
    });
  },

  // Animate cart icon when item is added
  animateCartIcon() {
    const cartIcon = document.querySelector('.cart-trigger');
    if (cartIcon) {
      cartIcon.classList.add('cart-bounce');
      setTimeout(() => {
        cartIcon.classList.remove('cart-bounce');
      }, 600);
    }
  },

  // Check if item is in cart
  isInCart(productId) {
    return this.cartItems.has(productId);
  }
};

// Initialize only when Snipcart is actually loaded
// This prevents console errors when Snipcart is lazy-loaded
window.initCartStateManager = function() {
  if (!window.cartStateManagerInitialized) {
    window.cartStateManagerInitialized = true;
    window.CartStateManager.init();
  }
};

// Auto-initialize if Snipcart is already available
if (window.Snipcart && window.Snipcart.api) {
  window.initCartStateManager();
}
