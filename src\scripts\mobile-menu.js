/**
 * Mobile Menu Handler
 * Manages mobile navigation menu toggle and interactions
 */

window.MobileMenu = {
  init() {
    const toggle = document.querySelector('.mobile-menu-toggle');
    const nav = document.querySelector('.main-nav');

    if (toggle && nav) {
      // Handle menu toggle
      toggle.addEventListener('click', () => {
        this.toggleMenu(toggle, nav);
      });

      // Close menu when clicking nav links
      nav.addEventListener('click', (e) => {
        if (e.target.tagName === 'A') {
          this.closeMenu(toggle, nav);
        }
      });

      // Close menu when clicking outside
      document.addEventListener('click', (e) => {
        if (!nav.contains(e.target) && !toggle.contains(e.target)) {
          this.closeMenu(toggle, nav);
        }
      });

      // Close menu on escape key
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && nav.classList.contains('nav-open')) {
          this.closeMenu(toggle, nav);
        }
      });
    }
  },

  toggleMenu(toggle, nav) {
    const isOpen = nav.classList.contains('nav-open');
    
    if (isOpen) {
      this.closeMenu(toggle, nav);
    } else {
      this.openMenu(toggle, nav);
    }
  },

  openMenu(toggle, nav) {
    nav.classList.add('nav-open');
    toggle.setAttribute('aria-expanded', 'true');
    toggle.classList.add('menu-open');
    
    // Prevent body scroll when menu is open
    document.body.style.overflow = 'hidden';
  },

  closeMenu(toggle, nav) {
    nav.classList.remove('nav-open');
    toggle.setAttribute('aria-expanded', 'false');
    toggle.classList.remove('menu-open');
    
    // Restore body scroll
    document.body.style.overflow = '';
  }
};

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.MobileMenu.init();
  });
} else {
  window.MobileMenu.init();
}
