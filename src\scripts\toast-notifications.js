/**
 * Toast Notification System
 * Provides user feedback for various actions throughout the site
 */

// Toast notification system
window.showToast = function(title, message, type = 'success', duration = 4000) {
  const container = document.getElementById('toast-container');
  if (!container) return;

  // Create toast element
  const toast = document.createElement('div');
  toast.className = `toast ${type}`;
  
  // Icons for different types
  const icons = {
    success: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <path d="M9 12l2 2 4-4"></path>
      <circle cx="12" cy="12" r="10"></circle>
    </svg>`,
    error: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <circle cx="12" cy="12" r="10"></circle>
      <line x1="15" y1="9" x2="9" y2="15"></line>
      <line x1="9" y1="9" x2="15" y2="15"></line>
    </svg>`,
    warning: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path>
      <line x1="12" y1="9" x2="12" y2="13"></line>
      <line x1="12" y1="17" x2="12.01" y2="17"></line>
    </svg>`
  };

  toast.innerHTML = `
    <div class="toast-icon">${icons[type] || icons.success}</div>
    <div class="toast-content">
      <div class="toast-title">${title}</div>
      <div class="toast-message">${message}</div>
    </div>
  `;

  // Add to container
  container.appendChild(toast);

  // Show toast with animation
  requestAnimationFrame(() => {
    toast.classList.add('show');
  });

  // Auto remove after duration
  setTimeout(() => {
    toast.classList.add('hide');
    toast.classList.remove('show');
    setTimeout(() => toast.remove(), 300);
  }, duration);

  return toast;
};

// Cart-specific toast functions
window.showCartSuccess = function(productName) {
  // Truncate long product names for better display
  const displayName = productName.length > 30 ? productName.substring(0, 30) + '...' : productName;
  return window.showToast(
    'Added to Cart!',
    `${displayName} added successfully`,
    'success',
    2500
  );
};

window.showCartError = function(message) {
  return window.showToast(
    'Cart Error',
    message || 'Unable to add item to cart. Please try again.',
    'error',
    4000
  );
};

window.showCartRemoved = function(productName) {
  return window.showToast(
    'Removed from Cart',
    `${productName} has been removed from your cart.`,
    'warning',
    3000
  );
};
