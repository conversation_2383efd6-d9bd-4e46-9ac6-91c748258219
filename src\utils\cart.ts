/**
 * Shopping cart utilities for Cheers Marketplace
 * Handles cart state management, localStorage persistence, and cart operations
 */

export interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  image?: string;
  slug: string;
  category: string;
  maxQuantity?: number; // For used goods, typically 1
}

export interface Cart {
  items: CartItem[];
  total: number;
  itemCount: number;
  updatedAt: string;
}

const CART_STORAGE_KEY = 'cheers_cart';

/**
 * Get cart from localStorage
 */
export function getCart(): Cart {
  if (typeof window === 'undefined') {
    return { items: [], total: 0, itemCount: 0, updatedAt: new Date().toISOString() };
  }

  try {
    const stored = localStorage.getItem(CART_STORAGE_KEY);
    if (stored) {
      const cart = JSON.parse(stored);
      return {
        ...cart,
        total: calculateTotal(cart.items),
        itemCount: calculateItemCount(cart.items)
      };
    }
  } catch (error) {
    console.warn('Error loading cart from localStorage:', error);
  }

  return { items: [], total: 0, itemCount: 0, updatedAt: new Date().toISOString() };
}

/**
 * Save cart to localStorage
 */
export function saveCart(cart: Cart): void {
  if (typeof window === 'undefined') return;

  try {
    const cartToSave = {
      ...cart,
      updatedAt: new Date().toISOString()
    };
    localStorage.setItem(CART_STORAGE_KEY, JSON.stringify(cartToSave));
    
    // Dispatch custom event for cart updates
    window.dispatchEvent(new CustomEvent('cartUpdated', { 
      detail: cartToSave 
    }));
  } catch (error) {
    console.warn('Error saving cart to localStorage:', error);
  }
}

/**
 * Add item to cart
 */
export function addToCart(product: Omit<CartItem, 'quantity'>): Cart {
  const cart = getCart();
  const existingItemIndex = cart.items.findIndex(item => item.id === product.id);

  if (existingItemIndex >= 0) {
    // Update quantity for existing item (max 1 for used goods)
    const existingItem = cart.items[existingItemIndex];
    const maxQty = product.maxQuantity || 1;
    const newQuantity = Math.min(existingItem.quantity + 1, maxQty);
    
    cart.items[existingItemIndex] = {
      ...existingItem,
      quantity: newQuantity
    };
  } else {
    // Add new item
    cart.items.push({
      ...product,
      quantity: 1,
      maxQuantity: product.maxQuantity || 1
    });
  }

  const updatedCart = {
    ...cart,
    total: calculateTotal(cart.items),
    itemCount: calculateItemCount(cart.items),
    updatedAt: new Date().toISOString()
  };

  saveCart(updatedCart);
  return updatedCart;
}

/**
 * Remove item from cart
 */
export function removeFromCart(productId: string): Cart {
  const cart = getCart();
  cart.items = cart.items.filter(item => item.id !== productId);

  const updatedCart = {
    ...cart,
    total: calculateTotal(cart.items),
    itemCount: calculateItemCount(cart.items),
    updatedAt: new Date().toISOString()
  };

  saveCart(updatedCart);
  return updatedCart;
}

/**
 * Update item quantity
 */
export function updateQuantity(productId: string, quantity: number): Cart {
  const cart = getCart();
  const itemIndex = cart.items.findIndex(item => item.id === productId);

  if (itemIndex >= 0) {
    if (quantity <= 0) {
      // Remove item if quantity is 0 or less
      cart.items.splice(itemIndex, 1);
    } else {
      // Update quantity (respect max quantity)
      const item = cart.items[itemIndex];
      const maxQty = item.maxQuantity || 1;
      cart.items[itemIndex].quantity = Math.min(quantity, maxQty);
    }
  }

  const updatedCart = {
    ...cart,
    total: calculateTotal(cart.items),
    itemCount: calculateItemCount(cart.items),
    updatedAt: new Date().toISOString()
  };

  saveCart(updatedCart);
  return updatedCart;
}

/**
 * Clear entire cart
 */
export function clearCart(): Cart {
  const emptyCart = {
    items: [],
    total: 0,
    itemCount: 0,
    updatedAt: new Date().toISOString()
  };

  saveCart(emptyCart);
  return emptyCart;
}

/**
 * Calculate total price
 */
function calculateTotal(items: CartItem[]): number {
  return items.reduce((total, item) => total + (item.price * item.quantity), 0);
}

/**
 * Calculate total item count
 */
function calculateItemCount(items: CartItem[]): number {
  return items.reduce((count, item) => count + item.quantity, 0);
}

/**
 * Format price for display
 */
export function formatPrice(price: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(price);
}

/**
 * Check if item is in cart
 */
export function isInCart(productId: string): boolean {
  const cart = getCart();
  return cart.items.some(item => item.id === productId);
}

/**
 * Get item quantity in cart
 */
export function getItemQuantity(productId: string): number {
  const cart = getCart();
  const item = cart.items.find(item => item.id === productId);
  return item ? item.quantity : 0;
}

/**
 * Generate cart summary for checkout
 */
export function getCartSummary(): {
  items: CartItem[];
  subtotal: number;
  tax: number;
  total: number;
  itemCount: number;
} {
  const cart = getCart();
  const subtotal = cart.total;
  const tax = subtotal * 0.0875; // 8.75% CA sales tax (adjust as needed)
  const total = subtotal + tax;

  return {
    items: cart.items,
    subtotal,
    tax,
    total,
    itemCount: cart.itemCount
  };
}

/**
 * Validate cart before checkout
 */
export function validateCart(): { valid: boolean; errors: string[] } {
  const cart = getCart();
  const errors: string[] = [];

  if (cart.items.length === 0) {
    errors.push('Cart is empty');
  }

  cart.items.forEach(item => {
    if (item.quantity <= 0) {
      errors.push(`Invalid quantity for ${item.name}`);
    }
    if (item.price <= 0) {
      errors.push(`Invalid price for ${item.name}`);
    }
  });

  return {
    valid: errors.length === 0,
    errors
  };
}
