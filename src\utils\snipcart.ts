/**
 * Snipcart integration utilities for Cheers Marketplace
 * Handles product validation and Snipcart-specific formatting
 */

import type { Product } from '../lib/products';

export interface SnipcartProduct {
  id: string;
  name: string;
  price: number;
  description: string;
  image: string;
  categories: string[];
  url: string;
  weight: number;
  dimensions: {
    width: number;
    height: number;
    length: number;
  };
  customFields: Array<{
    name: string;
    value: string;
    required: boolean;
  }>;
  metadata: {
    keyPoints: Array<{ label: string; value: string }>;
    defects: string | null;
    slug: string;
    createdAt?: string;
    updatedAt?: string;
  };
  maxQuantity: number;
  minQuantity: number;
  inventoryManagement: boolean;
  stock: number;
  allowOutOfStockPurchases: boolean;
  taxable: boolean;
  taxes: any[];
  shippable: boolean;
  currency: string;
  cheersMarketplace: {
    familyBusiness: boolean;
    personallyInspected: boolean;
    location: string;
    disclaimers: {
      colorAccuracy: string;
      petFriendly: string | null;
      qualityAssurance: string;
    };
  };
}

/**
 * Convert a Cheers Marketplace product to Snipcart format
 */
export function convertToSnipcartProduct(product: Product): SnipcartProduct {
  return {
    id: product.id,
    name: product.name,
    price: product.price,
    description: product.description,
    image: product.images?.[0] || '',
    categories: [product.category],
    url: `/products/${product.slug}`,
    weight: 0, // Used goods typically don't have standardized weights
    dimensions: {
      width: 0,
      height: 0,
      length: 0
    },
    customFields: [
      // No custom fields to prevent editable fields in cart
    ],
    metadata: {
      keyPoints: product.keyPoints || [],
      defects: product.defects || null,
      slug: product.slug || '',
      createdAt: product.createdAt,
      updatedAt: product.updatedAt
    },
    // For used goods, typically limit to 1 per item
    maxQuantity: 1,
    minQuantity: 1,
    // Mark as in stock (you might want to add inventory tracking later)
    inventoryManagement: false,
    stock: 1,
    allowOutOfStockPurchases: false,
    // Tax settings
    taxable: true,
    taxes: [],
    // Shipping settings  
    shippable: true,
    // Pricing
    currency: 'USD',
    // Additional Cheers Marketplace specific data
    cheersMarketplace: {
      familyBusiness: true,
      personallyInspected: true,
      location: 'Chico, CA',
      disclaimers: {
        colorAccuracy: 'Colors may appear slightly different due to lighting and display variations',
        petFriendly: product.category.toLowerCase().includes('clothing') || product.category.toLowerCase().includes('fabric') 
          ? 'Items are from a pet-friendly household with cats. All items are thoroughly cleaned.' 
          : null,
        qualityAssurance: 'All items are personally inspected for quality before listing'
      }
    }
  };
}

/**
 * Validate product data for Snipcart compatibility
 */
export function validateSnipcartProduct(product: Product): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Required fields
  if (!product.id) errors.push('Product ID is required');
  if (!product.name) errors.push('Product name is required');
  if (!product.price || product.price <= 0) errors.push('Product price must be greater than 0');
  if (!product.description) errors.push('Product description is required');

  // Snipcart-specific validations
  if (product.name.length > 255) errors.push('Product name must be 255 characters or less');
  if (product.description.length > 2000) errors.push('Product description must be 2000 characters or less');
  
  // Price validation
  if (product.price > 999999.99) errors.push('Product price must be less than $999,999.99');

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Generate Snipcart data attributes for HTML elements
 */
export function generateSnipcartAttributes(product: Product): Record<string, string> {
  // Include condition and category in the description for display purposes
  const enhancedDescription = `${product.description}\n\nCondition: Gently Used\nCategory: ${product.category}`;

  return {
    'data-item-id': product.id,
    'data-item-price': product.price.toString(),
    'data-item-url': `/products/${product.slug}`,
    'data-item-description': enhancedDescription,
    'data-item-image': product.images?.[0] || '',
    'data-item-name': product.name,
    'data-item-categories': product.category,
    'data-item-max-quantity': '1'
    // Removed custom fields to prevent editable condition/category fields in cart
  };
}

/**
 * Log product activity for debugging
 */
export function logProductActivity(action: string, productId: string, details?: any) {
  console.log(`[Snipcart] ${action}:`, productId, details ? JSON.stringify(details) : '');
}

/**
 * Check if product is available for purchase
 */
export function isProductAvailable(product: Product): boolean {
  // For used goods marketplace, products are typically unique items
  // You might want to add inventory tracking later
  return true;
}

/**
 * Format price for display
 */
export function formatPrice(price: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(price);
}

/**
 * Generate product URL for Snipcart validation
 */
export function generateProductValidationUrl(productId: string, baseUrl: string = ''): string {
  return `${baseUrl}/api/snipcart/products/${productId}`;
}
